from passlib.context import <PERSON><PERSON><PERSON>ontex<PERSON>
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Optional
from ..models.models import Admin
from ..core.config import settings
import hashlib

# Simple password hashing for compatibility
def simple_hash(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

def simple_verify(password: str, hashed: str) -> bool:
    return simple_hash(password) == hashed

class AuthService:
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return simple_verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """Hash a password"""
        return simple_hash(password)
    
    @staticmethod
    def authenticate_admin(db: Session, username: str, password: str) -> Optional[Admin]:
        """Authenticate admin user"""
        admin = db.query(Admin).filter(
            Admin.username == username,
            Admin.is_active == True
        ).first()
        
        if not admin:
            return None
            
        if not AuthService.verify_password(password, admin.password_hash):
            return None
            
        # Update last login
        admin.last_login = datetime.now()
        db.commit()
        
        return admin
    
    @staticmethod
    def create_admin(
        db: Session, 
        username: str, 
        email: str, 
        password: str, 
        full_name: str,
        is_super_admin: bool = False
    ) -> Admin:
        """Create a new admin user"""
        hashed_password = AuthService.get_password_hash(password)
        
        admin = Admin(
            username=username,
            email=email,
            password_hash=hashed_password,
            full_name=full_name,
            is_super_admin=is_super_admin
        )
        
        db.add(admin)
        db.commit()
        db.refresh(admin)
        
        return admin
    
    @staticmethod
    def get_admin_by_id(db: Session, admin_id: int) -> Optional[Admin]:
        """Get admin by ID"""
        return db.query(Admin).filter(
            Admin.id == admin_id,
            Admin.is_active == True
        ).first()
    
    @staticmethod
    def get_admin_by_username(db: Session, username: str) -> Optional[Admin]:
        """Get admin by username"""
        return db.query(Admin).filter(
            Admin.username == username,
            Admin.is_active == True
        ).first()

# Global auth service instance
auth_service = AuthService()
