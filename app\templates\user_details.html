<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ user.name }} - User Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .user-avatar-large {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #dee2e6;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .attendance-chart {
            height: 300px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-camera"></i> Face Attendance System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
                <a class="nav-link" href="/attendance">
                    <i class="fas fa-clock"></i> Attendance
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- User Header -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3 text-center">
                                {% if user.image_path %}
                                    <img src="{{ user.image_path }}" alt="{{ user.name }}" class="user-avatar-large">
                                {% else %}
                                    <i class="fas fa-user-circle fa-8x text-muted"></i>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h2>{{ user.name }}</h2>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-envelope"></i> {{ user.email }}
                                </p>
                                <p class="mb-2">
                                    <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }} fs-6">
                                        {{ 'Active' if user.is_active else 'Inactive' }}
                                    </span>
                                </p>
                                <p class="text-muted">
                                    <i class="fas fa-calendar"></i> 
                                    Registered: {{ user.created_at.strftime('%B %d, %Y') if user.created_at else 'Unknown' }}
                                </p>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-primary mb-2" data-bs-toggle="modal" data-bs-target="#editUserModal">
                                    <i class="fas fa-edit"></i> Edit Profile
                                </button>
                                <br>
                                <button class="btn btn-danger"
                                        onclick="deleteUser({{ user.id }}, '{{ user.name|replace("'", "\\'") }}')">
                                    <i class="fas fa-trash"></i> Delete User
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3>{{ stats.total_days }}</h3>
                        <p class="mb-0">Total Days</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3>{{ stats.total_hours }}</h3>
                        <p class="mb-0">Total Hours</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3>{{ (stats.total_hours / stats.total_days)|round(1) if stats.total_days > 0 else 0 }}</h3>
                        <p class="mb-0">Avg Hours/Day</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3>{{ logs|length }}</h3>
                        <p class="mb-0">Total Records</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Logs -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-history"></i> Attendance History</h5>
                        <div>
                            <button class="btn btn-sm btn-success" onclick="exportUserData()">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="timeRange" id="week" autocomplete="off" checked>
                                <label class="btn btn-outline-primary btn-sm" for="week">Week</label>
                                
                                <input type="radio" class="btn-check" name="timeRange" id="month" autocomplete="off">
                                <label class="btn btn-outline-primary btn-sm" for="month">Month</label>
                                
                                <input type="radio" class="btn-check" name="timeRange" id="all" autocomplete="off">
                                <label class="btn btn-outline-primary btn-sm" for="all">All</label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if logs %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Date</th>
                                            <th>Login Time</th>
                                            <th>Logout Time</th>
                                            <th>Duration</th>
                                            <th>Camera</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for log in logs %}
                                        <tr>
                                            <td>{{ log.date }}</td>
                                            <td>{{ log.login_time or '-' }}</td>
                                            <td>{{ log.logout_time or '-' }}</td>
                                            <td>
                                                {% if log.duration %}
                                                    <span class="badge bg-info">{{ log.duration }}</span>
                                                {% else %}
                                                    <span class="badge bg-warning">Active</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ log.camera_name }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if log.camera_type == 'IN' else 'warning' }}">
                                                    {{ log.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No attendance records found for this user.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit User Profile</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="/user/{{ user.id }}/edit" method="post" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="editName" class="form-label">Name</label>
                            <input type="text" class="form-control" id="editName" name="name" value="{{ user.name }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="editEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="editEmail" name="email" value="{{ user.email }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="editPhoto" class="form-label">Update Photo (Optional)</label>
                            <input type="file" class="form-control" id="editPhoto" name="file" accept="image/*">
                            <div class="form-text">Leave empty to keep current photo</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteUser(userId, userName) {
            console.log('Delete function called with userId:', userId, 'userName:', userName);
            if (confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone and will remove all attendance records for this user.`)) {
                console.log('User confirmed deletion, making request...');
                fetch(`/user/${userId}/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    console.log('Response received:', response);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.status === 'success') {
                        alert('User deleted successfully!');
                        // Redirect to users list page
                        window.location.href = '/users';
                    } else {
                        alert('Error deleting user: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the user: ' + error.message);
                });
            } else {
                console.log('User cancelled deletion');
            }
        }

        function exportUserData() {
            // Create a temporary link to download the PDF
            const userId = {{ user.id }};
            const link = document.createElement('a');
            link.href = `/user/${userId}/export-pdf`;
            link.download = `{{ user.name }}_attendance_report.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Time range filter functionality
        document.querySelectorAll('input[name="timeRange"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // TODO: Implement time range filtering
                console.log('Filter by:', this.id);
            });
        });
    </script>
</body>
</html>
