import datetime
from sqlalchemy.orm import Session
from ..models.models import Attendance, User
import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

# Cache to prevent duplicate attendance logs within a short time
attendance_cache = {}  # {user_id: last_seen_time}
ATTENDANCE_COOLDOWN = 300  # 5 minutes in seconds

class AttendanceService:
    def __init__(self):
        self.cache = attendance_cache

    def mark_attendance(self, user_id: int, camera_name: str, camera_type: str, db: Session) -> Dict:
        """
        Mark attendance for a user

        Args:
            user_id: ID of the user
            camera_name: Name of the camera
            camera_type: Type of camera (IN/OUT)
            db: Database session

        Returns:
            Dictionary with attendance status and message
        """
        logger.info(f"🔄 Attempting to mark attendance for user_id: {user_id}, camera: {camera_name}")

        now = datetime.datetime.now()
        today = now.date()
        current_time = now.strftime("%H:%M:%S")

        # Smart cooldown to prevent spam but allow proper IN/OUT flow
        cache_key = f"{user_id}_{camera_name}_{camera_type}_{today}"
        if cache_key in self.cache:
            time_diff = (now - self.cache[cache_key]).total_seconds()
            # Shorter cooldown for same camera type to prevent spam
            if time_diff < 60:  # 1 minute cooldown for same camera
                logger.info(f"⏰ Cooldown active for {user.name} on {camera_name} ({camera_type}) - {time_diff:.1f}s ago")
                return {
                    "status": "ignored",
                    "message": f"Attendance already recorded recently for this camera"
                }

        # Update cache after successful processing
        # Note: Cache will be updated at the end of successful processing

        try:
            # Check if user exists
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.error(f"❌ User not found with id: {user_id}")
                return {"status": "error", "message": "User not found"}

            logger.info(f"✅ User found: {user.name} (id: {user_id})")

            # Find camera by name
            from ..models.models import Camera
            camera = db.query(Camera).filter(Camera.name == camera_name).first()
            if not camera:
                logger.error(f"❌ Camera not found with name: {camera_name}")
                return {"status": "error", "message": f"Camera '{camera_name}' not found"}

            logger.info(f"✅ Camera found: {camera.name} (id: {camera.id})")

            # Handle attendance based on camera type
            if camera_type == "IN":
                # IN camera = Login time
                logger.info(f"🚪 IN camera detection - Processing LOGIN for {user.name}")

                # Find existing attendance record for today without logout
                existing = db.query(Attendance).filter(
                    Attendance.user_id == user_id,
                    Attendance.date == str(today),
                    Attendance.logout_time.is_(None)
                ).first()

                if existing:
                    # User already logged in today, ignore duplicate login
                    logger.info(f"⚠️ User {user.name} already logged in today at {existing.login_time} (record id: {existing.id})")
                    return {
                        "status": "ignored",
                        "message": f"User {user.name} already logged in today"
                    }
                else:
                    # No active login found, can create new login
                    logger.info(f"✅ No active login found for {user.name} today - creating new login record")
                    # Create new login record
                    new_log = Attendance(
                        user_id=user_id,
                        camera_id=camera.id,
                        date=str(today),
                        login_time=current_time,
                        logout_time=None,
                        camera_name=camera_name,
                        camera_type=camera_type
                    )
                    db.add(new_log)
                    db.commit()
                    db.refresh(new_log)

                    logger.info(f"✅ LOGIN recorded for user {user.name} at {current_time} (record id: {new_log.id})")

                    # Update cache after successful login
                    self.cache[cache_key] = now

                    return {
                        "status": "login",
                        "message": f"Login recorded for {user.name}",
                        "user_name": user.name,
                        "time": current_time,
                        "type": "login"
                    }

            elif camera_type == "OUT":
                # OUT camera = Logout time
                logger.info(f"🚪 OUT camera detection - Processing LOGOUT for {user.name}")

                # Find existing attendance record for today without logout
                existing = db.query(Attendance).filter(
                    Attendance.user_id == user_id,
                    Attendance.date == str(today),
                    Attendance.logout_time.is_(None)
                ).first()

                if existing:
                    # Update existing record with logout time
                    existing.logout_time = current_time
                    db.commit()

                    logger.info(f"✅ LOGOUT recorded for user {user.name} at {current_time}")

                    # Update cache after successful logout
                    self.cache[cache_key] = now

                    return {
                        "status": "logout",
                        "message": f"Logout recorded for {user.name}",
                        "user_name": user.name,
                        "time": current_time,
                        "type": "logout"
                    }
                else:
                    # No login record found, cannot logout
                    logger.warning(f"⚠️ Cannot logout {user.name} - no login record found for today")
                    return {
                        "status": "ignored",
                        "message": f"Cannot logout {user.name} - not logged in today"
                    }

            else:
                # Unknown camera type
                logger.error(f"❌ Unknown camera type: {camera_type}")
                return {
                    "status": "error",
                    "message": f"Unknown camera type: {camera_type}"
                }

        except Exception as e:
            logger.error(f"Error marking attendance: {e}")
            db.rollback()
            return {"status": "error", "message": str(e)}

    def get_current_logged_in_users(self, db: Session) -> list:
        """Get list of currently logged in users (no logout time today)"""
        today = datetime.date.today()

        current_users = db.query(Attendance, User).join(User).filter(
            Attendance.date == str(today),
            Attendance.logout_time.is_(None)
        ).all()

        return [
            {
                "user_id": attendance.user_id,
                "name": user.name,
                "email": user.email,
                "login_time": attendance.login_time,
                "camera_name": attendance.camera_name,
                "camera_type": attendance.camera_type
            }
            for attendance, user in current_users
        ]

    def get_attendance_logs(self, db: Session, date_filter: Optional[str] = None,
                          user_id: Optional[int] = None) -> list:
        """Get attendance logs with optional filters"""
        query = db.query(Attendance, User).join(User)

        if date_filter:
            query = query.filter(Attendance.date == date_filter)

        if user_id:
            query = query.filter(Attendance.user_id == user_id)

        logs = query.order_by(Attendance.date.desc(), Attendance.login_time.desc()).all()

        return [
            {
                "id": attendance.id,
                "user_id": attendance.user_id,
                "name": user.name,
                "email": user.email,
                "date": attendance.date,
                "login_time": attendance.login_time,
                "logout_time": attendance.logout_time,
                "camera_name": attendance.camera_name,
                "camera_type": attendance.camera_type,
                "duration": self._calculate_duration(attendance.login_time, attendance.logout_time)
            }
            for attendance, user in logs
        ]

    def _calculate_duration(self, login_time: str, logout_time: Optional[str]) -> Optional[str]:
        """Calculate duration between login and logout"""
        if not logout_time:
            return None

        try:
            login_dt = datetime.datetime.strptime(login_time, "%H:%M:%S")
            logout_dt = datetime.datetime.strptime(logout_time, "%H:%M:%S")
            duration = logout_dt - login_dt

            hours, remainder = divmod(duration.total_seconds(), 3600)
            minutes, _ = divmod(remainder, 60)

            return f"{int(hours):02d}:{int(minutes):02d}"
        except:
            return None

# Global instance
attendance_service = AttendanceService()

# Legacy function for backward compatibility
def mark_attendance(user_id, camera_name, camera_type, db):
    return attendance_service.mark_attendance(user_id, camera_name, camera_type, db)
