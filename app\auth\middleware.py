"""
Authentication middleware and dependencies for JWT-based authentication.
This module provides secure authentication checks and security headers.
"""

from fastapi import Request, Depends, HTTPException, status
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from typing import Op<PERSON>, Tuple
from app.core.database import get_database
from app.auth.utils import verify_token
from app.models.models import Admin

def get_current_admin(request: Request, db: Session = Depends(get_database)) -> Admin:
    """
    Get the current authenticated admin user.
    Raises HTTPException if not authenticated.
    """
    # Get token from cookie
    token = request.cookies.get("access_token")
    
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )
    
    # Verify token
    payload = verify_token(token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    # Get username from token
    username = payload.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    # Get admin from database
    admin = db.query(Admin).filter(Admin.username == username).first()
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    return admin

def get_current_admin_optional(request: Request, db: Session = Depends(get_database)) -> Optional[Admin]:
    """
    Get the current authenticated admin user, or None if not authenticated.
    Does not raise exceptions.
    """
    try:
        return get_current_admin(request, db)
    except HTTPException:
        return None

def check_admin_auth(request: Request, db: Session) -> Tuple[Optional[Admin], Optional[RedirectResponse]]:
    """
    Check if the current request is authenticated.
    Returns (admin, None) if authenticated, or (None, redirect_response) if not.
    """
    try:
        # Get token from cookie
        token = request.cookies.get("access_token")
        
        if not token:
            return None, RedirectResponse(url="/login", status_code=302)
        
        # Verify token
        payload = verify_token(token)
        if not payload:
            return None, RedirectResponse(url="/login", status_code=302)
        
        # Get username from token
        username = payload.get("sub")
        if not username:
            return None, RedirectResponse(url="/login", status_code=302)
        
        # Get admin from database
        admin = db.query(Admin).filter(Admin.username == username).first()
        if not admin:
            return None, RedirectResponse(url="/login", status_code=302)
        
        return admin, None
        
    except Exception:
        return None, RedirectResponse(url="/login", status_code=302)

def add_security_headers(response):
    """
    Add comprehensive security headers to prevent caching and improve security.
    This is crucial for preventing back button access after logout.
    """
    # Comprehensive cache prevention
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, private, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    response.headers["Last-Modified"] = "0"
    
    # Security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    
    # Content Security Policy
    response.headers["Content-Security-Policy"] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
        "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
        "font-src 'self' https://cdnjs.cloudflare.com; "
        "img-src 'self' data:;"
    )
    
    # Vary header for proper caching behavior
    response.headers["Vary"] = "Cookie, Authorization"
    
    return response
