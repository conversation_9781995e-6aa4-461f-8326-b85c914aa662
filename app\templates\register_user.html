<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register User - Face Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .preview-container {
            max-width: 300px;
            margin: 20px auto;
        }
        .preview-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }

        /* Active navbar item styling */
        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            color: #fff !important;
        }

        .navbar-nav .nav-link.active:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-eye"></i> VigilantEye
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link active fw-bold" href="/register-user">
                    <i class="fas fa-user-plus"></i> Register User
                </a>
                <a class="nav-link" href="/manage-camera">
                    <i class="fas fa-video"></i> Manage Cameras
                </a>
                <a class="nav-link" href="/attendance">
                    <i class="fas fa-clock"></i> Attendance Logs
                </a>
                <a class="nav-link" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
                <div class="navbar-nav ms-3">
                    <form method="post" action="/logout" style="margin: 0;">
                        <button type="submit" class="nav-link btn btn-link text-light border-0" style="background: none;">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-user-plus"></i> Register New User</h4>
                    </div>
                    <div class="card-body">
                        {% if message %}
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle"></i> {{ message }}
                            </div>
                        {% endif %}

                        <form method="post" enctype="multipart/form-data" id="registerForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">
                                            <i class="fas fa-user"></i> Full Name
                                        </label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope"></i> Email Address
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email" required>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">
                                            <i class="fas fa-camera"></i> Profile Photo
                                        </label>
                                        <div class="upload-area" onclick="document.getElementById('file').click()">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                            <p class="mb-0">Click to upload or drag and drop</p>
                                            <small class="text-muted">PNG, JPG, JPEG up to 5MB</small>
                                        </div>
                                        <input type="file" class="d-none" id="file" name="file"
                                               accept="image/*" required onchange="previewImage(this)">
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="preview-container">
                                        <div id="imagePreview" class="text-center">
                                            <i class="fas fa-user-circle fa-5x text-muted"></i>
                                            <p class="mt-2 text-muted">Photo preview will appear here</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/" class="btn btn-secondary me-md-2">
                                    <i class="fas fa-arrow-left"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Register User
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImage(input) {
            const preview = document.getElementById('imagePreview');

            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" class="preview-image" alt="Preview">`;
                };

                reader.readAsDataURL(input.files[0]);
            }
        }

        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.getElementById('file');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                previewImage(fileInput);
            }
        });

        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const file = document.getElementById('file').files[0];

            if (!name || !email || !file) {
                e.preventDefault();
                alert('Please fill in all fields and select a photo.');
                return;
            }

            // Check file size (5MB limit)
            if (file.size > 5 * 1024 * 1024) {
                e.preventDefault();
                alert('File size must be less than 5MB.');
                return;
            }

            // Check file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                e.preventDefault();
                alert('Please select a valid image file (JPEG, PNG, GIF).');
                return;
            }
        });
    </script>
</body>
</html>