<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Management - Face Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .camera-status {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .camera-active { background-color: #28a745; }
        .camera-inactive { background-color: #dc3545; }
        .camera-card {
            transition: transform 0.2s;
        }
        .camera-card:hover {
            transform: translateY(-2px);
        }

        /* Active navbar item styling */
        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            color: #fff !important;
        }

        .navbar-nav .nav-link.active:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-eye"></i> VigilantEye
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="/register-user">
                    <i class="fas fa-user-plus"></i> Register User
                </a>
                <a class="nav-link active fw-bold" href="/manage-camera">
                    <i class="fas fa-video"></i> Manage Cameras
                </a>
                <a class="nav-link" href="/attendance">
                    <i class="fas fa-clock"></i> Attendance Logs
                </a>
                <a class="nav-link" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
                <div class="navbar-nav ms-3">
                    <form method="post" action="/logout" style="margin: 0;">
                        <button type="submit" class="nav-link btn btn-link text-light border-0" style="background: none;">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h1 class="mb-4">
                    <i class="fas fa-video"></i> Camera Management
                </h1>
            </div>
        </div>

        <div class="row">
            <!-- Add Camera Form -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus"></i> Add New Camera</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" id="cameraForm">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-tag"></i> Camera Name
                                </label>
                                <input type="text" class="form-control" id="name" name="name"
                                       placeholder="e.g., Main Entrance" required>
                            </div>

                            <div class="mb-3">
                                <label for="camera_type" class="form-label">
                                    <i class="fas fa-arrows-alt-h"></i> Camera Type
                                </label>
                                <select class="form-select" id="camera_type" name="camera_type" required>
                                    <option value="">Select Type</option>
                                    <option value="IN">IN (Entry)</option>
                                    <option value="OUT">OUT (Exit)</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="url" class="form-label">
                                    <i class="fas fa-link"></i> RTSP URL / Camera Source
                                </label>
                                <input type="text" class="form-control" id="url" name="url"
                                       placeholder="rtsp://username:password@ip:port/stream or 0 for webcam" required>
                                <div class="form-text">
                                    Examples:<br>
                                    • RTSP: rtsp://admin:password@*************:554/stream<br>
                                    • Webcam: 0 (for default camera)<br>
                                    • HTTP: http://*************:8080/video
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Add Camera
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Camera List -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> Existing Cameras</h5>
                        <button class="btn btn-sm btn-success" onclick="startAllCameras()">
                            <i class="fas fa-play"></i> Start All
                        </button>
                    </div>
                    <div class="card-body">
                        {% if cameras %}
                            <div class="row">
                                {% for camera in cameras %}
                                <div class="col-md-6 mb-3">
                                    <div class="card camera-card h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">
                                                    <span class="camera-status camera-inactive"></span>
                                                    {{ camera.name }}
                                                </h6>
                                                <span class="badge bg-{% if camera.type == 'IN' %}success{% else %}warning{% endif %}">
                                                    {{ camera.type }}
                                                </span>
                                            </div>

                                            <p class="card-text">
                                                <small class="text-muted">
                                                    <i class="fas fa-link"></i> {{ camera.url[:50] }}{% if camera.url|length > 50 %}...{% endif %}
                                                </small>
                                            </p>

                                            <div class="btn-group w-100 mb-2" role="group">
                                                <button class="btn btn-sm btn-outline-success"
                                                        onclick="startCamera({{ camera.id }}, '{{ camera.name }}')">
                                                    <i class="fas fa-play"></i> Start
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        onclick="stopCamera({{ camera.id }})">
                                                    <i class="fas fa-stop"></i> Stop
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary"
                                                        onclick="viewCamera({{ camera.id }})">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                            </div>
                                            <div class="btn-group w-100" role="group">
                                                <button class="btn btn-sm btn-outline-secondary"
                                                        onclick="editCamera({{ camera.id }})">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                                <button class="btn btn-sm btn-outline-warning"
                                                        onclick="deleteCamera({{ camera.id }}, '{{ camera.name }}')">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-video fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No cameras configured yet. Add your first camera using the form on the left.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function startCamera(cameraId, cameraName) {
            fetch(`/start-camera/${cameraId}`, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.message) {
                        alert(`Success: ${data.message}`);
                        location.reload();
                    } else {
                        alert(`Error: ${data.error}`);
                    }
                })
                .catch(error => {
                    alert('Error starting camera');
                    console.error(error);
                });
        }

        function stopCamera(cameraId) {
            if (confirm('Are you sure you want to stop this camera?')) {
                fetch(`/stop-camera/${cameraId}`, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message || data.error);
                        location.reload();
                    })
                    .catch(error => {
                        alert('Error stopping camera');
                        console.error(error);
                    });
            }
        }

        function viewCamera(cameraId) {
            window.open(`/camera-stream/${cameraId}`, '_blank', 'width=800,height=600');
        }

        function editCamera(cameraId) {
            // Fetch camera details
            fetch(`/get-camera/${cameraId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const camera = data.camera;

                        // Populate form with current values
                        document.getElementById('name').value = camera.name;
                        document.getElementById('camera_type').value = camera.type;
                        document.getElementById('url').value = camera.url;

                        // Change form to edit mode
                        const form = document.getElementById('cameraForm');
                        const submitBtn = form.querySelector('button[type="submit"]');

                        // Update form action and button text
                        form.setAttribute('data-edit-id', cameraId);
                        submitBtn.innerHTML = '<i class="fas fa-save"></i> Update Camera';
                        submitBtn.className = 'btn btn-warning';

                        // Add cancel button
                        if (!document.getElementById('cancelEdit')) {
                            const cancelBtn = document.createElement('button');
                            cancelBtn.type = 'button';
                            cancelBtn.id = 'cancelEdit';
                            cancelBtn.className = 'btn btn-secondary mt-2';
                            cancelBtn.innerHTML = '<i class="fas fa-times"></i> Cancel Edit';
                            cancelBtn.onclick = cancelEdit;
                            submitBtn.parentNode.appendChild(cancelBtn);
                        }

                        // Update form header
                        document.querySelector('.card-header h5').innerHTML = '<i class="fas fa-edit"></i> Edit Camera';

                        // Scroll to form
                        document.getElementById('cameraForm').scrollIntoView({ behavior: 'smooth' });
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error fetching camera details');
                });
        }

        function cancelEdit() {
            const form = document.getElementById('cameraForm');
            const submitBtn = form.querySelector('button[type="submit"]');
            const cancelBtn = document.getElementById('cancelEdit');

            // Reset form
            form.reset();
            form.removeAttribute('data-edit-id');

            // Reset button
            submitBtn.innerHTML = '<i class="fas fa-plus"></i> Add Camera';
            submitBtn.className = 'btn btn-primary';

            // Remove cancel button
            if (cancelBtn) {
                cancelBtn.remove();
            }

            // Reset header
            document.querySelector('.card-header h5').innerHTML = '<i class="fas fa-plus"></i> Add New Camera';
        }

        function deleteCamera(cameraId, cameraName) {
            if (confirm(`Are you sure you want to delete camera "${cameraName}"?\n\nThis action cannot be undone and will stop the camera if it's currently running.`)) {
                const button = event.target.closest('button');
                const originalText = button.innerHTML;

                // Show loading state
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
                button.disabled = true;

                fetch(`/delete-camera/${cameraId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert(`Camera "${cameraName}" deleted successfully!`);
                        // Refresh the page to show updated camera list
                        location.reload();
                    } else if (data.message && data.message.includes('attendance records')) {
                        // Ask if user wants to force delete
                        if (confirm(`${data.message}\n\nDo you want to force delete this camera and all its attendance records?\n\nWARNING: This will permanently delete all attendance data for this camera!`)) {
                            // Force delete with attendance records
                            fetch(`/delete-camera/${cameraId}?force=true`, {
                                method: 'DELETE',
                                headers: {
                                    'Content-Type': 'application/json',
                                }
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.status === 'success') {
                                    alert(`Camera "${cameraName}" and all its attendance records deleted successfully!`);
                                    location.reload();
                                } else {
                                    alert('Error: ' + data.message);
                                }
                                button.innerHTML = originalText;
                                button.disabled = false;
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                alert('Error force deleting camera: ' + error.message);
                                button.innerHTML = originalText;
                                button.disabled = false;
                            });
                        } else {
                            // User cancelled force delete
                            button.innerHTML = originalText;
                            button.disabled = false;
                        }
                    } else {
                        alert('Error: ' + data.message);
                        // Restore button state on error
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting camera: ' + error.message);
                    // Restore button state on error
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        }

        function startAllCameras() {
            if (confirm('Start all cameras? This may take a moment.')) {
                // TODO: Implement start all functionality
                alert('Start all functionality coming soon!');
            }
        }

        // Form submission handler
        document.getElementById('cameraForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('name').value.trim();
            const type = document.getElementById('camera_type').value;
            const url = document.getElementById('url').value.trim();
            const editId = this.getAttribute('data-edit-id');

            if (!name || !type || !url) {
                alert('Please fill in all fields.');
                return;
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            if (editId) {
                // Edit mode
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
                submitBtn.disabled = true;

                const formData = new FormData();
                formData.append('name', name);
                formData.append('url', url);
                formData.append('camera_type', type);

                fetch(`/edit-camera/${editId}`, {
                    method: 'PUT',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert(data.message);
                        cancelEdit();
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error updating camera');
                })
                .finally(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                });
            } else {
                // Add mode - use original form submission
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
                submitBtn.disabled = true;

                // Submit form normally for add
                this.submit();
            }
        });
    </script>
</body>
</html>