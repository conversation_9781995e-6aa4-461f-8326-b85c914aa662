"""
OpenVINO-based Face Recognition System
High-performance CPU-optimized face recognition using OpenVINO
"""

import numpy as np
import cv2
from typing import List, Optional, Dict, Tuple
import logging
import time
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenVINOFaceRecognitionSystem:
    def __init__(self, similarity_threshold: float = 0.5, model_package: str = "buffalo_l"):
        """
        Initialize OpenVINO Face Recognition System
        
        Args:
            similarity_threshold: Cosine similarity threshold (higher = more strict)
            model_package: Model package (buffalo_l for ArcFace, buffalo_s for MobileFaceNet)
        """
        self.similarity_threshold = similarity_threshold
        self.model_package = model_package
        self.known_encodings = []
        self.known_names = []
        self.known_user_ids = []
        
        # Performance metrics
        self.detection_times = []
        self.recognition_times = []
        self.total_detections = 0
        self.successful_recognitions = 0
        
        # OpenVINO components
        self.core = None
        self.detection_model = None
        self.recognition_model = None
        self.compiled_detection = None
        self.compiled_recognition = None
        
        # Model paths
        self.models_dir = None
        
        # Initialize OpenVINO system
        self._initialize_openvino_system()
    
    def _initialize_openvino_system(self):
        """Initialize OpenVINO system with ONNX models directly"""
        try:
            import openvino as ov
            self.core = ov.Core()
            
            # Find InsightFace models
            home_dir = Path.home()
            models_dir = home_dir / ".insightface" / "models" / self.model_package
            
            if not models_dir.exists():
                logger.error(f"❌ Model directory not found: {models_dir}")
                raise Exception(f"Model package {self.model_package} not found")
            
            self.models_dir = models_dir
            
            # Load detection model
            if self.model_package == "buffalo_l":
                det_path = models_dir / "det_10g.onnx"
                rec_path = models_dir / "w600k_r50.onnx"
            else:  # buffalo_s
                det_path = models_dir / "det_500m.onnx"
                rec_path = models_dir / "w600k_mbf.onnx"
            
            if det_path.exists():
                logger.info(f"🔄 Loading detection model: {det_path.name}")
                self.detection_model = self.core.read_model(str(det_path))
                
                # Compile for CPU with optimizations
                self.compiled_detection = self.core.compile_model(
                    self.detection_model, 
                    "CPU",
                    {
                        "PERFORMANCE_HINT": "LATENCY",
                        "CPU_THREADS_NUM": "0",
                        "INFERENCE_PRECISION_HINT": "f32"
                    }
                )
                logger.info("✅ Detection model loaded successfully")
            
            if rec_path.exists():
                logger.info(f"🔄 Loading recognition model: {rec_path.name}")
                self.recognition_model = self.core.read_model(str(rec_path))
                
                # Compile for CPU with optimizations
                self.compiled_recognition = self.core.compile_model(
                    self.recognition_model,
                    "CPU", 
                    {
                        "PERFORMANCE_HINT": "LATENCY",
                        "CPU_THREADS_NUM": "0", 
                        "INFERENCE_PRECISION_HINT": "f32"
                    }
                )
                logger.info("✅ Recognition model loaded successfully")
            
            logger.info(f"✅ OpenVINO {self.model_package} system initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ OpenVINO initialization failed: {e}")
            raise Exception(f"Failed to initialize OpenVINO system: {e}")
    
    def _preprocess_detection_input(self, image: np.ndarray, target_size: Tuple[int, int] = (640, 640)) -> np.ndarray:
        """Preprocess image for detection model"""
        # Resize image
        resized = cv2.resize(image, target_size)
        
        # Convert BGR to RGB and normalize
        rgb_image = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        
        # Normalize to [0, 1] and then to [-1, 1]
        normalized = rgb_image.astype(np.float32) / 255.0
        normalized = (normalized - 0.5) / 0.5
        
        # Add batch dimension and transpose to NCHW
        input_tensor = np.transpose(normalized, (2, 0, 1))
        input_tensor = np.expand_dims(input_tensor, axis=0)
        
        return input_tensor
    
    def _preprocess_recognition_input(self, face_image: np.ndarray, target_size: Tuple[int, int] = (112, 112)) -> np.ndarray:
        """Preprocess face image for recognition model"""
        # Resize face
        resized = cv2.resize(face_image, target_size)
        
        # Convert BGR to RGB and normalize
        rgb_face = cv2.cvtColor(resized, cv2.COLOR_BGR2RGB)
        
        # Normalize to [0, 1] and then apply standard normalization
        normalized = rgb_face.astype(np.float32)
        normalized = (normalized - 127.5) / 128.0
        
        # Add batch dimension and transpose to NCHW
        input_tensor = np.transpose(normalized, (2, 0, 1))
        input_tensor = np.expand_dims(input_tensor, axis=0)
        
        return input_tensor
    
    def _postprocess_detection(self, detection_output: np.ndarray, original_shape: Tuple[int, int], 
                              input_shape: Tuple[int, int] = (640, 640), conf_threshold: float = 0.5) -> List[Dict]:
        """Post-process detection output to get face bounding boxes"""
        faces = []
        
        try:
            # Detection output format: [batch, num_anchors, 15] 
            # where 15 = [x1, y1, x2, y2, conf, 5 landmarks (x,y pairs)]
            detections = detection_output[0]
            
            # Scale factors
            scale_x = original_shape[1] / input_shape[0]
            scale_y = original_shape[0] / input_shape[1]
            
            for detection in detections:
                confidence = detection[4]
                
                if confidence > conf_threshold:
                    # Scale bounding box back to original image size
                    x1 = int(detection[0] * scale_x)
                    y1 = int(detection[1] * scale_y)
                    x2 = int(detection[2] * scale_x)
                    y2 = int(detection[3] * scale_y)
                    
                    # Ensure coordinates are within image bounds
                    x1 = max(0, min(x1, original_shape[1]))
                    y1 = max(0, min(y1, original_shape[0]))
                    x2 = max(0, min(x2, original_shape[1]))
                    y2 = max(0, min(y2, original_shape[0]))
                    
                    if x2 > x1 and y2 > y1:  # Valid bounding box
                        faces.append({
                            'bbox': (x1, y1, x2, y2),
                            'confidence': confidence,
                            'landmarks': detection[5:15].reshape(5, 2) * [scale_x, scale_y]
                        })
            
        except Exception as e:
            logger.error(f"❌ Detection post-processing error: {e}")
        
        return faces
    
    def detect_faces(self, image: np.ndarray) -> List[Dict]:
        """Detect faces in image using OpenVINO detection model"""
        if self.compiled_detection is None:
            logger.error("❌ Detection model not loaded")
            return []
        
        try:
            start_time = time.time()
            
            # Preprocess input
            input_tensor = self._preprocess_detection_input(image)
            
            # Run inference
            detection_output = self.compiled_detection([input_tensor])
            
            # Get output tensor
            output_tensor = list(detection_output.values())[0]
            
            # Post-process to get face bounding boxes
            faces = self._postprocess_detection(output_tensor, image.shape[:2])
            
            detection_time = time.time() - start_time
            self.detection_times.append(detection_time)
            self.total_detections += len(faces)
            
            logger.debug(f"🔍 Detected {len(faces)} faces in {detection_time*1000:.2f}ms")
            
            return faces
            
        except Exception as e:
            logger.error(f"❌ Face detection error: {e}")
            return []
    
    def extract_face_encoding(self, face_image: np.ndarray) -> Optional[np.ndarray]:
        """Extract face encoding using OpenVINO recognition model"""
        if self.compiled_recognition is None:
            logger.error("❌ Recognition model not loaded")
            return None
        
        try:
            # Preprocess face image
            input_tensor = self._preprocess_recognition_input(face_image)
            
            # Run inference
            recognition_output = self.compiled_recognition([input_tensor])
            
            # Get embedding
            embedding = list(recognition_output.values())[0][0]
            
            # Normalize embedding
            embedding = embedding / np.linalg.norm(embedding)
            
            return embedding
            
        except Exception as e:
            logger.error(f"❌ Face encoding extraction error: {e}")
            return None
    
    def load_known_faces(self, users_data: List[Dict]):
        """Load known faces from database"""
        self.known_encodings = []
        self.known_names = []
        self.known_user_ids = []
        
        for user in users_data:
            if user['face_encoding']:
                try:
                    # Try to load as float32 (InsightFace format)
                    encoding = np.frombuffer(user['face_encoding'], dtype=np.float32)
                    
                    # Support both MobileFaceNet (128D) and ArcFace (512D) embeddings
                    if len(encoding) in [128, 512]:
                        # Normalize the embedding
                        encoding = encoding / np.linalg.norm(encoding)
                        
                        self.known_encodings.append(encoding)
                        self.known_names.append(user['name'])
                        self.known_user_ids.append(user['id'])
                        
                        model_type = "MobileFaceNet" if len(encoding) == 128 else "ArcFace"
                        logger.debug(f"Loaded {model_type} (128D) encoding for {user['name']}")
                    else:
                        logger.warning(f"Unsupported encoding dimension {len(encoding)} for {user['name']}")
                        
                except Exception as e:
                    logger.error(f"Error loading encoding for user {user['name']}: {e}")
        
        logger.info(f"✅ Loaded {len(self.known_encodings)} known face embeddings for OpenVINO recognition")
        if len(self.known_encodings) > 0:
            logger.info(f"   Registered users: {', '.join(self.known_names)}")
    
    def recognize_faces_in_frame(self, frame: np.ndarray) -> List[Dict]:
        """Recognize faces in frame using OpenVINO models"""
        if len(self.known_encodings) == 0:
            return []
        
        results = []
        
        try:
            # Detect faces
            faces = self.detect_faces(frame)
            
            for face_info in faces:
                bbox = face_info['bbox']
                x1, y1, x2, y2 = bbox
                
                # Extract face region
                face_image = frame[y1:y2, x1:x2]
                
                if face_image.size == 0:
                    continue
                
                # Extract face encoding
                start_time = time.time()
                embedding = self.extract_face_encoding(face_image)
                recognition_time = time.time() - start_time
                
                if embedding is None:
                    continue
                
                # Compare with known faces
                name = "Unknown"
                user_id = None
                confidence = 0.0
                
                if len(self.known_encodings) > 0:
                    similarities = []
                    for known_embedding in self.known_encodings:
                        # Check dimension compatibility
                        if len(embedding) == len(known_embedding):
                            similarity = np.dot(embedding, known_embedding)
                            similarities.append(similarity)
                        else:
                            similarities.append(0.0)
                    
                    similarities = np.array(similarities)
                    best_match_index = np.argmax(similarities)
                    best_similarity = similarities[best_match_index]
                    
                    if best_similarity >= self.similarity_threshold:
                        name = self.known_names[best_match_index]
                        user_id = self.known_user_ids[best_match_index]
                        confidence = best_similarity
                        self.successful_recognitions += 1
                        logger.info(f"👤 FACE RECOGNIZED: {name} (ID: {user_id}, similarity: {confidence:.3f})")
                
                self.recognition_times.append(recognition_time)
                
                results.append({
                    'name': name,
                    'user_id': user_id,
                    'confidence': confidence,
                    'location': (y1, x2, y2, x1),  # top, right, bottom, left
                    'is_known': user_id is not None,
                    'detection_time': face_info.get('detection_time', 0),
                    'recognition_time': recognition_time
                })
        
        except Exception as e:
            logger.error(f"❌ Error in OpenVINO recognition: {e}")
        
        return results
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        if len(self.detection_times) == 0:
            return {}
        
        return {
            'avg_detection_time': np.mean(self.detection_times) * 1000,  # ms
            'avg_recognition_time': np.mean(self.recognition_times) * 1000 if self.recognition_times else 0,  # ms
            'total_detections': self.total_detections,
            'successful_recognitions': self.successful_recognitions,
            'recognition_rate': self.successful_recognitions / max(1, self.total_detections) * 100
        }

# Global instance for the application
openvino_face_system = None

def initialize_openvino_system(model_package: str = "buffalo_l", similarity_threshold: float = 0.5):
    """Initialize global OpenVINO face recognition system"""
    global openvino_face_system
    try:
        openvino_face_system = OpenVINOFaceRecognitionSystem(
            similarity_threshold=similarity_threshold,
            model_package=model_package
        )
        logger.info("✅ OpenVINO face recognition system initialized")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to initialize OpenVINO system: {e}")
        return False
