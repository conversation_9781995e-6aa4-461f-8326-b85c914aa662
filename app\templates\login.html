<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vigilant Eye - Face Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Navigation */
        .header {
            background: white;
            padding: 1rem 0;
            position: relative;
            z-index: 1000;
        }

        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: #333 !important;
            text-decoration: none;
            font-size: 1.2rem;
        }

        .brand-icon {
            width: 35px;
            height: 35px;
            background: #20c997;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            color: white;
            font-size: 1rem;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 2.5rem;
        }

        .nav-links a {
            color: #666;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #20c997;
        }

        .admin-signup-btn {
            background: #20c997;
            color: white;
            padding: 0.6rem 1.8rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .admin-signup-btn:hover {
            background: #1ba085;
            color: white;
            transform: translateY(-1px);
        }

        /* Main Content */
        .main-content {
            display: flex;
            min-height: calc(100vh - 80px);
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .content-section {
            flex: 1;
            padding: 4rem 2rem 4rem 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 300;
            color: #333;
            margin-bottom: 2rem;
            line-height: 1.1;
        }

        .hero-description {
            font-size: 1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            max-width: 450px;
        }

        .hero-subtitle {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 3rem;
            max-width: 400px;
        }

        .demo-btn {
            background: #20c997;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            width: fit-content;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .demo-btn:hover {
            background: #1ba085;
            transform: translateY(-2px);
            color: white;
        }

        .additional-info {
            font-size: 0.8rem;
            color: #999;
            margin-top: 1rem;
        }

        /* Login Section */
        .login-section {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            background: url('/static/face.jpg') center/cover no-repeat;
        }

        .login-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .login-container {
            width: 100%;
            max-width: 350px;
            position: relative;
            z-index: 10;
            padding: 2rem;
        }

        .login-title {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-title h3 {
            color: #333;
            font-weight: 300;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 1px 3px rgba(255,255,255,0.8);
        }

        .login-title p {
            color: #666;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-shadow: 0 1px 3px rgba(255,255,255,0.8);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 0.8rem 1.2rem;
            font-size: 0.9rem;
            color: #333;
            transition: all 0.3s ease;
            width: 100%;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(32, 201, 151, 0.5);
            box-shadow: 0 0 0 2px rgba(32, 201, 151, 0.2);
            outline: none;
        }

        .form-control::placeholder {
            color: #666;
            font-size: 0.85rem;
        }

        .btn-demo {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
            padding: 0.8rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-demo:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(32, 201, 151, 0.4);
        }

        .btn-login {
            background: linear-gradient(135deg, #343a40, #495057);
            color: white;
            padding: 0.8rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-login:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(52, 58, 64, 0.4);
        }

        /* Alert Styling */
        .alert {
            border-radius: 8px;
            border: none;
            margin-bottom: 1.5rem;
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border-left: 4px solid #dc3545;
            padding: 1rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .content-section {
                padding: 2rem 1rem;
            }

            .login-section {
                padding: 2rem 1rem;
            }
        }

        /* Content Sections */
        .content-section-page {
            padding: 5rem 0;
            background: #f8f9fa;
        }

        .content-section-page:nth-child(even) {
            background: white;
        }

        .section-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 300;
            color: #333;
            margin-bottom: 3rem;
        }

        /* Features Section */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.8rem;
        }

        .feature-card h3 {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        /* Pricing Section */
        .pricing-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .pricing-section .section-title {
            color: white;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .pricing-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            position: relative;
            transition: transform 0.3s ease;
            color: #333;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
        }

        .pricing-card.featured {
            transform: scale(1.05);
            border: 3px solid #20c997;
        }

        .popular-badge {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #20c997;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .pricing-header h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #20c997;
            margin-bottom: 2rem;
        }

        .price span {
            font-size: 1rem;
            color: #666;
        }

        .pricing-features {
            list-style: none;
            padding: 0;
            margin: 2rem 0;
        }

        .pricing-features li {
            padding: 0.5rem 0;
            color: #666;
        }

        .pricing-features i {
            color: #20c997;
            margin-right: 0.5rem;
        }

        .pricing-btn {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .pricing-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(32, 201, 151, 0.4);
        }

        /* About Section */
        .about-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .about-main-title {
            text-align: center;
            margin-bottom: 4rem;
        }

        .about-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: #2c3e50;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .title-underline {
            width: 120px;
            height: 4px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            margin: 0 auto;
            border-radius: 2px;
        }

        .about-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .about-hero {
            text-align: center;
            margin-bottom: 5rem;
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .about-hero h2 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            font-weight: 700;
            line-height: 1.3;
        }

        .about-lead {
            color: #5a6c7d;
            line-height: 1.8;
            font-size: 1.2rem;
            max-width: 900px;
            margin: 0 auto;
            font-weight: 400;
        }

        .about-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 5rem;
        }

        .overview-card {
            background: white;
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-top: 4px solid #20c997;
        }

        .overview-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 50px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .card-header i {
            font-size: 2rem;
            color: #20c997;
        }

        .card-header h3 {
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .overview-card p {
            color: #5a6c7d;
            line-height: 1.7;
            font-size: 1.1rem;
            margin: 0;
        }

        .why-choose-section {
            margin-bottom: 5rem;
        }

        .section-subtitle {
            color: #2c3e50;
            font-size: 2.2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .section-subtitle::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            border-radius: 2px;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .benefit-item {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            text-align: center;
        }

        .benefit-item:hover {
            transform: translateY(-5px);
        }

        .benefit-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.8rem;
        }

        .benefit-item h4 {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .benefit-item p {
            color: #5a6c7d;
            line-height: 1.6;
            font-size: 1rem;
            margin: 0;
        }

        .tech-showcase {
            margin-bottom: 5rem;
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .tech-intro {
            text-align: center;
            color: #5a6c7d;
            font-size: 1.1rem;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .tech-specs-single-line {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
        }

        .spec-item {
            background: #f8f9fa;
            padding: 2rem 1rem;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease, background 0.3s ease;
            border: 2px solid transparent;
        }

        .spec-item:hover {
            transform: translateY(-5px);
            background: white;
            border-color: #20c997;
            box-shadow: 0 8px 25px rgba(32, 201, 151, 0.15);
        }

        .spec-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .spec-item strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .spec-item p {
            color: #5a6c7d;
            font-size: 0.9rem;
            line-height: 1.4;
            margin: 0;
        }

        .stats-section {
            text-align: center;
        }

        .stats-intro {
            text-align: center;
            color: #5a6c7d;
            font-size: 1.1rem;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .stat-item {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
            padding: 2.5rem 2rem;
            border-radius: 20px;
            text-align: center;
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            pointer-events: none;
        }

        .stat-item:hover {
            transform: translateY(-8px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .stat-label {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.3rem;
            position: relative;
            z-index: 1;
        }

        .stat-description {
            font-size: 0.9rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* Contact Section */
        .contact-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            padding: 3rem 0;
        }

        .contact-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .contact-main-title {
            text-align: center;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .contact-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .contact-hero {
            text-align: center;
            margin-bottom: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .contact-hero h2 {
            color: #20c997;
            font-size: 1.5rem;
            margin-bottom: 0.8rem;
            font-weight: 700;
        }

        .contact-lead {
            color: #ecf0f1;
            font-size: 1rem;
            line-height: 1.5;
            max-width: 500px;
            margin: 0 auto;
        }

        .contact-cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-primary,
        .cta-secondary {
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            font-size: 1rem;
        }

        .cta-primary {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .cta-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        .cta-secondary {
            background: transparent;
            color: #20c997;
            border: 2px solid #20c997;
        }

        .cta-secondary:hover {
            background: #20c997;
            color: white;
            transform: translateY(-3px);
        }

        .contact-methods {
            margin-bottom: 5rem;
            position: relative;
            z-index: 1;
        }

        .contact-methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .contact-method-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s ease, background 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .contact-method-card:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.15);
        }

        .method-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.8rem;
        }

        .contact-method-card h4 {
            color: white;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .contact-method-card p {
            color: #bdc3c7;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .method-btn {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .method-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(32, 201, 151, 0.4);
        }

        .availability {
            color: #95a5a6;
            font-size: 0.85rem;
            font-style: italic;
        }

        /* Demo Section */
        .demo-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            padding: 3rem 0;
        }

        .demo-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="demo-grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23demo-grid)"/></svg>');
            pointer-events: none;
        }

        .demo-main-title {
            text-align: center;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .demo-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .demo-hero {
            text-align: center;
            margin-bottom: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .demo-hero h2 {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 0.8rem;
            font-weight: 700;
        }

        .demo-lead {
            color: #f8f9fa;
            font-size: 1rem;
            line-height: 1.5;
            max-width: 500px;
            margin: 0 auto;
        }

        .demo-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            position: relative;
            z-index: 1;
        }

        .demo-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-benefits h3 {
            color: #20c997;
            font-size: 1.1rem;
            margin-bottom: 0.8rem;
            font-weight: 700;
        }

        .demo-features-list {
            list-style: none;
            padding: 0;
            margin-bottom: 1rem;
        }

        .demo-features-list li {
            display: flex;
            align-items: center;
            gap: 0.6rem;
            margin-bottom: 0.5rem;
            color: #f8f9fa;
            font-size: 0.85rem;
        }

        .demo-features-list i {
            color: #20c997;
            font-size: 0.8rem;
        }

        .demo-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.6rem;
        }

        .demo-stats .stat-item {
            background: rgba(32, 201, 151, 0.2);
            border: 1px solid rgba(32, 201, 151, 0.3);
            border-radius: 10px;
            padding: 0.8rem;
            text-align: center;
        }

        .demo-stats .stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: #20c997;
            margin-bottom: 0.2rem;
        }

        .demo-stats .stat-label {
            color: #f8f9fa;
            font-size: 0.7rem;
        }

        .demo-form {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-form h3 {
            color: #20c997;
            font-size: 1.1rem;
            margin-bottom: 0.8rem;
            font-weight: 700;
        }

        .demo-btn {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(32, 201, 151, 0.4);
        }

        .demo-note {
            color: #adb5bd;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .demo-note i {
            color: #20c997;
        }

        .contact-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            position: relative;
            z-index: 1;
        }

        .contact-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-info h3 {
            color: #20c997;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 0.8rem;
            padding: 0.8rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .contact-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .contact-text strong {
            color: #20c997;
            display: block;
            margin-bottom: 0.2rem;
            font-size: 0.9rem;
        }

        .contact-text p {
            color: #f8f9fa;
            margin: 0;
            line-height: 1.3;
            font-size: 0.8rem;
        }

        .contact-form {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-form h3 {
            color: #20c997;
            font-size: 1.1rem;
            margin-bottom: 0.8rem;
            font-weight: 700;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .contact-card h4 {
            color: #20c997;
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .contact-item i {
            background: #20c997;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            flex-shrink: 0;
            margin-top: 0.2rem;
        }

        .contact-item strong {
            color: #20c997;
            display: block;
            margin-bottom: 0.3rem;
            font-size: 0.9rem;
        }

        .contact-item p {
            color: #bdc3c7;
            margin: 0;
            line-height: 1.4;
        }

        .contact-item small {
            color: #95a5a6;
            font-size: 0.8rem;
            display: block;
            margin-top: 0.2rem;
        }

        .location-item {
            margin-bottom: 1.5rem;
        }

        .location-item strong {
            color: #20c997;
            display: block;
            margin-bottom: 0.5rem;
        }

        .location-item p {
            color: #bdc3c7;
            line-height: 1.5;
            margin: 0;
        }

        .demo-benefits {
            margin: 1rem 0 1.5rem;
        }

        .benefit-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: #bdc3c7;
            font-size: 0.9rem;
        }

        .benefit-item i {
            color: #20c997;
            font-size: 0.8rem;
        }

        .demo-schedule-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 0.9rem;
        }

        .demo-schedule-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .contact-form {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1;
        }

        .form-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .contact-form h3 {
            color: #20c997;
            font-size: 1.8rem;
            margin-bottom: 1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .form-header p {
            color: #ecf0f1;
            margin-bottom: 1.5rem;
            line-height: 1.6;
            font-size: 1.1rem;
        }

        .form-benefits {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .form-benefit {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #bdc3c7;
            font-size: 0.9rem;
        }

        .form-benefit i {
            color: #20c997;
            font-size: 1rem;
        }

        /* Sales & Support Section */
        .sales-support-section {
            margin-bottom: 5rem;
            position: relative;
            z-index: 1;
        }

        .support-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .support-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, background 0.3s ease;
        }

        .support-card:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.15);
        }

        .support-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .support-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .support-header h4 {
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }

        .support-content p {
            color: #e9ecef;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .contact-methods {
            margin-bottom: 2rem;
        }

        .contact-method {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 0.8rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .contact-method i {
            color: #20c997;
            font-size: 1rem;
            width: 20px;
        }

        .contact-method strong {
            color: #20c997;
            margin-right: 0.5rem;
        }

        .contact-method a {
            color: #f8f9fa;
            text-decoration: none;
        }

        .contact-method a:hover {
            color: #20c997;
        }

        .support-btn {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .support-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(32, 201, 151, 0.4);
        }

        /* Locations Section */
        .locations-section {
            margin-bottom: 5rem;
            position: relative;
            z-index: 1;
        }

        .locations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .location-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .location-card:hover {
            transform: translateY(-8px);
        }

        .location-header {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }

        .location-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.8rem;
        }

        .location-header h4 {
            color: white;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .location-badge {
            background: #20c997;
            color: white;
            padding: 0.3rem 1rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .location-content {
            text-align: left;
        }

        .address {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .address i {
            color: #20c997;
            font-size: 1.2rem;
            margin-top: 0.2rem;
        }

        .address p {
            color: #f8f9fa;
            margin: 0;
            line-height: 1.5;
        }

        .location-details {
            margin-bottom: 2rem;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.8rem;
            color: #e9ecef;
            font-size: 0.95rem;
        }

        .detail-item i {
            color: #20c997;
            width: 20px;
        }

        .location-btn {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .location-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(32, 201, 151, 0.4);
        }

        .location-btn.disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .location-btn.disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .contact-form .form-row,
        .demo-form .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.8rem;
            margin-bottom: 0.6rem;
        }

        .contact-form .form-group,
        .demo-form .form-group {
            margin-bottom: 0.6rem;
        }

        .contact-form label,
        .demo-form label {
            font-size: 0.85rem;
            margin-bottom: 0.3rem;
        }

        .contact-form input,
        .contact-form select,
        .contact-form textarea,
        .demo-form input,
        .demo-form select,
        .demo-form textarea {
            padding: 0.6rem;
            font-size: 0.85rem;
        }

        .contact-form label {
            color: #20c997;
            display: block;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .contact-form input,
        .contact-form textarea,
        .contact-form select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.8rem;
            color: white;
            font-size: 0.9rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .contact-form input::placeholder,
        .contact-form textarea::placeholder {
            color: #95a5a6;
        }

        .contact-form input:focus,
        .contact-form textarea:focus,
        .contact-form select:focus {
            outline: none;
            border-color: #20c997;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 2px rgba(32, 201, 151, 0.2);
        }

        .contact-form select option {
            background: #2c3e50;
            color: white;
        }

        .contact-btn {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1rem;
        }

        .contact-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(32, 201, 151, 0.4);
        }

        /* Message Section */
        .message-section {
            margin-bottom: 5rem;
            position: relative;
            z-index: 1;
        }

        .message-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            margin-top: 3rem;
        }

        .message-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 2.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .message-info h4 {
            color: #20c997;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .message-info > p {
            color: #e9ecef;
            margin-bottom: 2rem;
            line-height: 1.6;
            font-size: 1.1rem;
        }

        .message-benefits {
            margin-bottom: 2rem;
        }

        .message-benefits .benefit-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .message-benefits .benefit-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #20c997, #17a2b8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .benefit-content strong {
            color: #20c997;
            display: block;
            margin-bottom: 0.3rem;
            font-size: 1rem;
        }

        .benefit-content p {
            color: #e9ecef;
            margin: 0;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .contact-guarantee {
            background: rgba(32, 201, 151, 0.1);
            border: 1px solid rgba(32, 201, 151, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .guarantee-icon {
            width: 50px;
            height: 50px;
            background: #20c997;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .guarantee-content strong {
            color: #20c997;
            display: block;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .guarantee-content p {
            color: #f8f9fa;
            margin: 0;
            line-height: 1.5;
            font-size: 0.95rem;
        }

        .contact-form .form-header h4 {
            color: #20c997;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .radio-group {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
        }

        .radio-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #e9ecef;
            cursor: pointer;
            font-size: 0.95rem;
        }

        .radio-label input[type="radio"] {
            display: none;
        }

        .radio-custom {
            width: 18px;
            height: 18px;
            border: 2px solid #20c997;
            border-radius: 50%;
            position: relative;
            transition: all 0.3s ease;
        }

        .radio-label input[type="radio"]:checked + .radio-custom::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #20c997;
            border-radius: 50%;
        }

        .form-note {
            color: #adb5bd;
            font-size: 0.85rem;
            margin-top: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-note i {
            color: #20c997;
        }

        .contact-footer {
            margin-top: 4rem;
            padding-top: 3rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .footer-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.05);
            padding: 1.5rem;
            border-radius: 15px;
        }

        .footer-item i {
            color: #20c997;
            font-size: 1.5rem;
            margin-top: 0.2rem;
        }

        .footer-item strong {
            color: #20c997;
            display: block;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .footer-item p {
            color: #e9ecef;
            margin: 0;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        /* Responsive Design for New Sections */
        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
            }

            .contact-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .contact-form .form-row {
                grid-template-columns: 1fr;
            }

            .contact-methods-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .demo-content,
            .contact-content {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .demo-stats {
                grid-template-columns: repeat(3, 1fr);
                gap: 0.5rem;
            }

            .demo-stats .stat-item {
                padding: 0.6rem;
            }

            .demo-stats .stat-number {
                font-size: 1rem;
            }

            .demo-stats .stat-label {
                font-size: 0.65rem;
            }

            .support-grid {
                grid-template-columns: 1fr;
            }

            .locations-grid {
                grid-template-columns: 1fr;
            }

            .message-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .footer-content {
                grid-template-columns: 1fr;
            }

            .radio-group {
                flex-direction: column;
                gap: 1rem;
            }

            .tech-specs {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .about-overview {
                grid-template-columns: 1fr;
            }

            .benefits-grid {
                grid-template-columns: 1fr;
            }

            .tech-specs-single-line {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .about-title {
                font-size: 2.5rem;
            }

            .about-hero h2 {
                font-size: 2rem;
            }

            .about-lead {
                font-size: 1rem;
            }

            .section-subtitle {
                font-size: 1.8rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .content-section-page {
                padding: 3rem 0;
            }

            .contact-footer {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .contact-title {
                font-size: 2.5rem;
            }

            .contact-hero h2 {
                font-size: 2rem;
            }

            .contact-lead {
                font-size: 1rem;
            }

            .contact-methods-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .stat-number {
                font-size: 2rem;
            }

            .tech-specs-single-line {
                grid-template-columns: 1fr;
            }

            .about-title {
                font-size: 2rem;
            }

            .about-hero h2 {
                font-size: 1.6rem;
            }

            .section-container {
                padding: 0 1rem;
            }

            .overview-card,
            .benefit-item {
                min-width: auto;
            }

            .contact-title,
            .demo-title {
                font-size: 1.8rem;
            }

            .contact-hero h2,
            .demo-hero h2 {
                font-size: 1.2rem;
            }

            .contact-hero,
            .demo-hero {
                padding: 1rem;
                margin-bottom: 1.5rem;
            }

            .contact-form,
            .contact-info,
            .demo-form,
            .demo-info {
                padding: 1rem;
            }

            .demo-section,
            .contact-section {
                min-height: auto;
                padding: 1.5rem 0;
            }

            .demo-features-list li {
                font-size: 0.8rem;
                margin-bottom: 0.4rem;
            }

            .contact-item {
                padding: 0.6rem;
            }

            .contact-text strong {
                font-size: 0.85rem;
            }

            .contact-text p {
                font-size: 0.75rem;
            }

            .demo-stats {
                grid-template-columns: repeat(3, 1fr);
                gap: 0.5rem;
            }

            .demo-stats .stat-item {
                padding: 1rem;
            }

            .demo-stats .stat-number {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <div class="navbar-container">
            <a href="#" class="navbar-brand">
                <div class="brand-icon">
                    <i class="fas fa-eye"></i>
                </div>
                Vigilant Eye
            </a>
            <nav class="nav-links">
                <a href="#about">About</a>
                <a href="#features">Features</a>
                <a href="#pricing">Pricing</a>
                <a href="#demo">Demo</a>
                <a href="#contact">Contact</a>
                <a href="#admin" class="admin-signup-btn" onclick="scrollToLogin()">ADMIN SIGNUP</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Content Section -->
        <div class="content-section">
            <h1 class="hero-title">Vigilant Eye</h1>
            <p class="hero-description">
                Advanced CPU-optimized face recognition attendance system designed for real-time monitoring and automated attendance tracking. Experience seamless integration with multiple camera types, comprehensive user management, and detailed reporting capabilities.
            </p>
            <p class="hero-subtitle">
                Intelligent attendance tracking powered by cutting-edge face recognition technology.
            </p>
            <a href="#demo" class="demo-btn">
                BOOK A DEMO
            </a>
            <p class="additional-info">
                Book a meeting with a specialist today.
            </p>
        </div>

        <!-- Login Section -->
        <div class="login-section" id="admin">
            <div class="login-container">
                <div class="login-title">
                    <h3>Vigilant Eye</h3>
                    <p>ADMIN DASHBOARD - LOGIN</p>
                </div>

                {% if error %}
                <div class="alert" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ error }}
                </div>
                {% endif %}

                <form method="post" action="/login">
                    <div class="form-group">
                        <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                    </div>

                    <div class="form-group">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                    </div>

                    <button type="button" class="btn-demo" onclick="alert('Demo functionality coming soon!')">
                        DEMO
                    </button>

                    <button type="submit" class="btn-login">
                        LOGIN
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scroll to login section
        function scrollToLogin() {
            document.getElementById('admin').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Auto-focus on username field when login section is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    document.getElementById('username').focus();
                }
            });
        });
        observer.observe(document.getElementById('admin'));

        // Add loading state to form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.btn-login');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
            submitBtn.disabled = true;
        });
    </script>

    <!-- About Section -->
    <section id="about" class="content-section-page about-section">
        <div class="section-container">
            <div class="about-main-title">
                <h1 class="about-title">About Vigilant Eye</h1>
                <div class="title-underline"></div>
            </div>

            <div class="about-content">
                <div class="about-hero">
                    <h2>Revolutionizing Attendance Management with Advanced AI Technology</h2>
                    <p class="about-lead">Vigilant Eye represents the next generation of intelligent attendance management systems. Our cutting-edge face recognition technology, powered by advanced AI algorithms and optimized for CPU performance, delivers unparalleled accuracy and reliability for organizations worldwide. We've engineered a solution that combines enterprise-grade security with user-friendly operation, making sophisticated attendance tracking accessible to businesses of all sizes.</p>
                </div>

                <div class="about-overview">
                    <div class="overview-card">
                        <div class="card-header">
                            <i class="fas fa-bullseye"></i>
                            <h3>Our Vision</h3>
                        </div>
                        <p>To transform workplace efficiency through intelligent automation, making attendance management seamless, secure, and insightful for every organization.</p>
                    </div>

                    <div class="overview-card">
                        <div class="card-header">
                            <i class="fas fa-rocket"></i>
                            <h3>Our Mission</h3>
                        </div>
                        <p>Democratizing advanced face recognition technology by delivering accessible, affordable, and efficient solutions that empower organizations to focus on what matters most - their people and productivity.</p>
                    </div>

                    <div class="overview-card">
                        <div class="card-header">
                            <i class="fas fa-star"></i>
                            <h3>Our Values</h3>
                        </div>
                        <p>Innovation, reliability, security, and customer success drive everything we do. We're committed to delivering solutions that exceed expectations and adapt to evolving business needs.</p>
                    </div>
                </div>

                <!-- <div class="why-choose-section">
                    <h3 class="section-subtitle">Why Organizations Choose Vigilant Eye</h3>
                    <div class="benefits-grid">
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <h4>CPU Optimized Performance</h4>
                            <p>Advanced algorithms designed for CPU efficiency, eliminating the need for expensive GPU hardware while maintaining exceptional accuracy.</p>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h4>Enterprise-Grade Security</h4>
                            <p>Bank-level encryption, secure data handling, and privacy-first architecture ensure your sensitive information remains protected.</p>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </div>
                            <h4>Infinitely Scalable</h4>
                            <p>From small teams to enterprise deployments with 10,000+ employees, our architecture scales seamlessly with your growth.</p>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h4>Multi-Platform Support</h4>
                            <p>Native compatibility across Windows, Linux, and macOS with flexible deployment options including cloud and on-premise.</p>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <h4>Real-Time Processing</h4>
                            <p>Instant face recognition and attendance logging with sub-second response times for seamless user experience.</p>
                        </div>
                        <div class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-plug"></i>
                            </div>
                            <h4>Easy Integration</h4>
                            <p>Comprehensive RESTful APIs, webhooks, and SDK support for seamless integration with existing HR and payroll systems.</p>
                        </div>
                    </div>
                </div> -->

                <div class="tech-showcase">
                    <h3 class="section-subtitle">Technical Excellence & Innovation</h3>
                    <p class="tech-intro">Built on cutting-edge technology stack for maximum performance, reliability, and scalability.</p>
                    <div class="tech-specs-single-line">
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-brain"></i></div>
                            <strong>AI Engine</strong>
                            <p>InsightFace ArcFace-R100 with OpenCV fallback</p>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-database"></i></div>
                            <strong>Database</strong>
                            <p>MySQL with SQLAlchemy ORM</p>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-server"></i></div>
                            <strong>Backend</strong>
                            <p>FastAPI with async processing</p>
                        </div>
                        <div class="spec-item">
                            <div class="spec-icon"><i class="fas fa-video"></i></div>
                            <strong>Camera Support</strong>
                            <p>RTSP, USB, IP multi-stream</p>
                        </div>
                    </div>
                </div>

                <div class="stats-section">
                    <h3 class="section-subtitle">Proven Track Record</h3>
                    <p class="stats-intro">Numbers that speak to our commitment to excellence and customer satisfaction.</p>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">99.7%</div>
                            <div class="stat-label">Recognition Accuracy</div>
                            <div class="stat-description">Industry-leading precision</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">Organizations</div>
                            <div class="stat-description">Trust our platform</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50K+</div>
                            <div class="stat-label">Daily Recognitions</div>
                            <div class="stat-description">Processed seamlessly</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">99.9%</div>
                            <div class="stat-label">System Uptime</div>
                            <div class="stat-description">Reliable 24/7 operation</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="content-section-page">
        <div class="section-container">
            <h2 class="section-title">Features</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3>Real-time Face Recognition</h3>
                    <p>CPU-optimized face detection and recognition with advanced algorithms for accurate identification without requiring GPU acceleration.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3>Multi-Camera Support</h3>
                    <p>Support for RTSP cameras, webcams, and IP cameras with simultaneous monitoring across multiple entry/exit points.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>Automated Attendance</h3>
                    <p>Automatic login/logout detection with intelligent time tracking and attendance cooldown to prevent duplicate entries.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>User Management</h3>
                    <p>Complete user registration, profile management, and photo upload system with comprehensive user database.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Admin Dashboard</h3>
                    <p>Real-time monitoring dashboard with live camera feeds, attendance statistics, and system overview.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3>Detailed Reports</h3>
                    <p>Comprehensive attendance logs with filtering, export capabilities, and detailed analytics for better insights.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="content-section-page pricing-section">
        <div class="section-container">
            <h2 class="section-title">Pricing Plans</h2>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Starter</h3>
                        <div class="price">$299<span>/month</span></div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Up to 50 users</li>
                        <li><i class="fas fa-check"></i> 2 camera streams</li>
                        <li><i class="fas fa-check"></i> Basic reporting</li>
                        <li><i class="fas fa-check"></i> Email support</li>
                        <li><i class="fas fa-check"></i> Cloud storage (10GB)</li>
                    </ul>
                    <button class="pricing-btn">Get Started</button>
                </div>
                <div class="pricing-card featured">
                    <div class="pricing-header">
                        <h3>Professional</h3>
                        <div class="price">$599<span>/month</span></div>
                        <div class="popular-badge">Most Popular</div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Up to 200 users</li>
                        <li><i class="fas fa-check"></i> 10 camera streams</li>
                        <li><i class="fas fa-check"></i> Advanced analytics</li>
                        <li><i class="fas fa-check"></i> Priority support</li>
                        <li><i class="fas fa-check"></i> Cloud storage (100GB)</li>
                        <li><i class="fas fa-check"></i> API access</li>
                    </ul>
                    <button class="pricing-btn">Get Started</button>
                </div>
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3>Enterprise</h3>
                        <div class="price">Custom</div>
                    </div>
                    <ul class="pricing-features">
                        <li><i class="fas fa-check"></i> Unlimited users</li>
                        <li><i class="fas fa-check"></i> Unlimited cameras</li>
                        <li><i class="fas fa-check"></i> Custom integrations</li>
                        <li><i class="fas fa-check"></i> 24/7 support</li>
                        <li><i class="fas fa-check"></i> On-premise deployment</li>
                        <li><i class="fas fa-check"></i> Custom features</li>
                    </ul>
                    <button class="pricing-btn">Contact Sales</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="content-section-page" style="background: #f8f9fa; min-height: 100vh; padding: 2rem 0;">
        <div class="section-container" style="max-width: 1200px; margin: 0 auto;">
            <!-- Compact Header -->
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="color: #333; font-size: 2.5rem; font-weight: 800; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 2px;">REQUEST A DEMO</h1>
                <div style="width: 120px; height: 4px; background: linear-gradient(135deg, #20c997, #17a2b8); margin: 0 auto 1rem; border-radius: 2px;"></div>
                <h2 style="color: #20c997; font-size: 1.3rem; margin-bottom: 0.5rem; font-weight: 700;">See Vigilant Eye in Action</h2>
                <p style="color: #666; font-size: 1rem; max-width: 600px; margin: 0 auto;">Experience the power of our face recognition attendance system with a personalized demonstration.</p>
            </div>

            <!-- Main Content Grid -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: start;">
                <!-- Demo Information -->
                <div style="background: white; border-radius: 15px; padding: 1.5rem; border: 1px solid #e9ecef; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h3 style="color: #333; font-size: 1.1rem; margin-bottom: 1rem; font-weight: 700;">What You'll See in the Demo</h3>

                    <!-- Demo Features -->
                    <div style="margin-bottom: 1.5rem;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.4rem;">
                                <i class="fas fa-check-circle" style="color: #20c997; font-size: 0.8rem;"></i>
                                <span style="color: #555; font-size: 0.8rem;">Real-time face recognition</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.4rem;">
                                <i class="fas fa-check-circle" style="color: #20c997; font-size: 0.8rem;"></i>
                                <span style="color: #555; font-size: 0.8rem;">Live attendance tracking</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.4rem;">
                                <i class="fas fa-check-circle" style="color: #20c997; font-size: 0.8rem;"></i>
                                <span style="color: #555; font-size: 0.8rem;">Multi-camera setup</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.4rem;">
                                <i class="fas fa-check-circle" style="color: #20c997; font-size: 0.8rem;"></i>
                                <span style="color: #555; font-size: 0.8rem;">User management</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.4rem;">
                                <i class="fas fa-check-circle" style="color: #20c997; font-size: 0.8rem;"></i>
                                <span style="color: #555; font-size: 0.8rem;">Dashboard analytics</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.4rem;">
                                <i class="fas fa-check-circle" style="color: #20c997; font-size: 0.8rem;"></i>
                                <span style="color: #555; font-size: 0.8rem;">System integration</span>
                            </div>
                        </div>
                    </div>

                    <!-- Demo Stats -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem; margin-top: 1rem;">
                        <div style="text-align: center; background: #f8f9fa; border-radius: 10px; padding: 1rem; border: 1px solid #e9ecef;">
                            <div style="color: #20c997; font-size: 1.5rem; font-weight: 800; margin-bottom: 0.2rem;">30</div>
                            <div style="color: #666; font-size: 0.75rem; font-weight: 500;">Minutes Demo</div>
                        </div>
                        <div style="text-align: center; background: #f8f9fa; border-radius: 10px; padding: 1rem; border: 1px solid #e9ecef;">
                            <div style="color: #20c997; font-size: 1.5rem; font-weight: 800; margin-bottom: 0.2rem;">Free</div>
                            <div style="color: #666; font-size: 0.75rem; font-weight: 500;">No Cost</div>
                        </div>
                        <div style="text-align: center; background: #f8f9fa; border-radius: 10px; padding: 1rem; border: 1px solid #e9ecef;">
                            <div style="color: #20c997; font-size: 1.5rem; font-weight: 800; margin-bottom: 0.2rem;">24h</div>
                            <div style="color: #666; font-size: 0.75rem; font-weight: 500;">Quick Setup</div>
                        </div>
                    </div>
                </div>

                <!-- Demo Form -->
                <div style="background: white; border-radius: 15px; padding: 1.5rem; border: 1px solid #e9ecef; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h3 style="color: #333; font-size: 1.1rem; margin-bottom: 0.8rem; font-weight: 700;">Schedule Your Demo</h3>
                    <form>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.6rem; margin-bottom: 0.6rem;">
                            <div>
                                <label style="color: #333; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Full Name *</label>
                                <input type="text" placeholder="Enter your full name" required style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 0.6rem; color: #333; font-size: 0.85rem; width: 100%;">
                            </div>
                            <div>
                                <label style="color: #333; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Email Address *</label>
                                <input type="email" placeholder="Enter your email" required style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 0.6rem; color: #333; font-size: 0.85rem; width: 100%;">
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.6rem; margin-bottom: 0.6rem;">
                            <div>
                                <label style="color: #333; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Company Name *</label>
                                <input type="text" placeholder="Your company name" required style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 0.6rem; color: #333; font-size: 0.85rem; width: 100%;">
                            </div>
                            <div>
                                <label style="color: #333; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Phone Number</label>
                                <input type="tel" placeholder="Your phone number" style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 0.6rem; color: #333; font-size: 0.85rem; width: 100%;">
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.6rem; margin-bottom: 0.6rem;">
                            <div>
                                <label style="color: #333; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Company Size</label>
                                <select style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 0.6rem; color: #333; font-size: 0.85rem; width: 100%;">
                                    <option value="">Select company size</option>
                                    <option value="1-10">1-10 employees</option>
                                    <option value="11-50">11-50 employees</option>
                                    <option value="51-200">51-200 employees</option>
                                    <option value="201-1000">201-1000 employees</option>
                                    <option value="1000+">1000+ employees</option>
                                </select>
                            </div>
                            <div>
                                <label style="color: #333; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Preferred Demo Time</label>
                                <select style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 0.6rem; color: #333; font-size: 0.85rem; width: 100%;">
                                    <option value="">Select preferred time</option>
                                    <option value="morning">Morning (9AM-12PM EST)</option>
                                    <option value="afternoon">Afternoon (12PM-5PM EST)</option>
                                    <option value="evening">Evening (5PM-7PM EST)</option>
                                </select>
                            </div>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <label style="color: #333; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Additional Requirements</label>
                            <textarea placeholder="Tell us about your specific needs, current setup, or any questions you have..." rows="2" style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 0.6rem; color: #333; font-size: 0.85rem; width: 100%; resize: vertical;"></textarea>
                        </div>
                        <button type="submit" style="background: linear-gradient(135deg, #20c997, #17a2b8); color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 25px; font-weight: 600; cursor: pointer; font-size: 0.9rem; width: 100%; display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-calendar-check"></i>
                            Schedule Demo
                        </button>
                        <p style="color: #666; font-size: 0.75rem; display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin: 0;">
                            <i class="fas fa-info-circle" style="color: #20c997;"></i>
                            We'll contact you within 24 hours to confirm your demo appointment.
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="content-section-page contact-section" style="min-height: 100vh; padding: 2rem 0;">
        <div class="section-container" style="max-width: 1200px; margin: 0 auto;">
            <!-- Compact Header -->
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="color: white; font-size: 2.5rem; font-weight: 800; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 2px;">CONTACT US</h1>
                <div style="width: 120px; height: 4px; background: linear-gradient(135deg, #20c997, #17a2b8); margin: 0 auto 1rem; border-radius: 2px;"></div>
                <h2 style="color: #20c997; font-size: 1.3rem; margin-bottom: 0.5rem; font-weight: 700;">Get in Touch</h2>
                <p style="color: #ecf0f1; font-size: 1rem; max-width: 500px; margin: 0 auto;">Have questions about Vigilant Eye? Need support? We're here to help you succeed.</p>
            </div>

            <!-- Compact Two-Column Layout -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: start;">
                <!-- Contact Information -->
                <div style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(15px); border-radius: 15px; padding: 1.5rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                    <h3 style="color: #20c997; font-size: 1.1rem; margin-bottom: 1rem; font-weight: 700;">Contact Information</h3>
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: flex-start; gap: 0.8rem;">
                            <div style="width: 35px; height: 35px; background: linear-gradient(135deg, #20c997, #17a2b8); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem; flex-shrink: 0;">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <strong style="color: #20c997; display: block; margin-bottom: 0.2rem; font-size: 0.9rem;">Email</strong>
                                <p style="color: #f8f9fa; margin: 0; line-height: 1.3; font-size: 0.8rem;"><EMAIL><br><EMAIL></p>
                            </div>
                        </div>

                        <div style="display: flex; align-items: flex-start; gap: 0.8rem;">
                            <div style="width: 35px; height: 35px; background: linear-gradient(135deg, #20c997, #17a2b8); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem; flex-shrink: 0;">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <strong style="color: #20c997; display: block; margin-bottom: 0.2rem; font-size: 0.9rem;">Phone</strong>
                                <p style="color: #f8f9fa; margin: 0; line-height: 1.3; font-size: 0.8rem;">+****************<br>Mon-Fri, 9AM-6PM EST</p>
                            </div>
                        </div>

                        <div style="display: flex; align-items: flex-start; gap: 0.8rem;">
                            <div style="width: 35px; height: 35px; background: linear-gradient(135deg, #20c997, #17a2b8); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem; flex-shrink: 0;">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <strong style="color: #20c997; display: block; margin-bottom: 0.2rem; font-size: 0.9rem;">Address</strong>
                                <p style="color: #f8f9fa; margin: 0; line-height: 1.3; font-size: 0.8rem;">123 Innovation Drive<br>Tech Valley, CA 94025<br>United States</p>
                            </div>
                        </div>

                        <div style="display: flex; align-items: flex-start; gap: 0.8rem;">
                            <div style="width: 35px; height: 35px; background: linear-gradient(135deg, #20c997, #17a2b8); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.9rem; flex-shrink: 0;">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <strong style="color: #20c997; display: block; margin-bottom: 0.2rem; font-size: 0.9rem;">Business Hours</strong>
                                <p style="color: #f8f9fa; margin: 0; line-height: 1.3; font-size: 0.8rem;">Monday - Friday: 9AM - 6PM EST<br>Emergency Support: 24/7</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(15px); border-radius: 15px; padding: 1.5rem; border: 1px solid rgba(255, 255, 255, 0.2);">
                    <h3 style="color: #20c997; font-size: 1.1rem; margin-bottom: 0.8rem; font-weight: 700;">Send us a Message</h3>
                    <form>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.6rem; margin-bottom: 0.6rem;">
                            <div>
                                <label style="color: #20c997; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Full Name *</label>
                                <input type="text" placeholder="Enter your full name" required style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 0.6rem; color: white; font-size: 0.85rem; width: 100%;">
                            </div>
                            <div>
                                <label style="color: #20c997; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Email Address *</label>
                                <input type="email" placeholder="Enter your email" required style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 0.6rem; color: white; font-size: 0.85rem; width: 100%;">
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.6rem; margin-bottom: 0.6rem;">
                            <div>
                                <label style="color: #20c997; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Company</label>
                                <input type="text" placeholder="Your company name" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 0.6rem; color: white; font-size: 0.85rem; width: 100%;">
                            </div>
                            <div>
                                <label style="color: #20c997; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Phone Number</label>
                                <input type="tel" placeholder="Your phone number" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 0.6rem; color: white; font-size: 0.85rem; width: 100%;">
                            </div>
                        </div>
                        <div style="margin-bottom: 0.6rem;">
                            <label style="color: #20c997; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Subject *</label>
                            <select required style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 0.6rem; color: white; font-size: 0.85rem; width: 100%;">
                                <option value="" style="background: #2c3e50; color: white;">Select a topic</option>
                                <option value="general" style="background: #2c3e50; color: white;">General Inquiry</option>
                                <option value="pricing" style="background: #2c3e50; color: white;">Pricing Information</option>
                                <option value="technical" style="background: #2c3e50; color: white;">Technical Support</option>
                                <option value="partnership" style="background: #2c3e50; color: white;">Partnership</option>
                                <option value="other" style="background: #2c3e50; color: white;">Other</option>
                            </select>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <label style="color: #20c997; display: block; margin-bottom: 0.3rem; font-size: 0.85rem; font-weight: 500;">Message *</label>
                            <textarea placeholder="Tell us how we can help you..." rows="3" required style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 0.6rem; color: white; font-size: 0.85rem; width: 100%; resize: vertical;"></textarea>
                        </div>
                        <button type="submit" style="background: linear-gradient(135deg, #20c997, #17a2b8); color: white; border: none; padding: 0.8rem 1.5rem; border-radius: 25px; font-weight: 600; cursor: pointer; font-size: 0.9rem; width: 100%; display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-paper-plane"></i>
                            Send Message
                        </button>
                        <p style="color: #adb5bd; font-size: 0.75rem; display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin: 0;">
                            <i class="fas fa-info-circle" style="color: #20c997;"></i>
                            We'll respond within 24 hours during business hours.
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
