<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vigilant Eye Dashboard</title>
    <!-- Google Fonts - Poppins for modern typography -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* CSS Variables for consistent theming */
        :root {
            --primary-color: #20B2AA; /* Light Sea Green */
            --secondary-color: #333; /* Dark gray for text/accents */
            --text-dark: #333;
            --text-light: #555;
            --background-light: #f8f8f8;
            --card-background: #ffffff;
            --border-color: #e0e0e0; /* Lighter border for softer look */
            --shadow-light: rgba(0, 0, 0, 0.06); /* Softer shadow */
            --shadow-medium: rgba(0, 0, 0, 0.12); /* Slightly more pronounced for hover */
            --gradient-start: #20B2AA;
            --gradient-end: #3CB371;
            --status-online: #28a745; /* Green */
            --status-offline: #dc3545; /* Red */
            --status-in: #28a745;
            --status-out: #dc3545;
            --accent-purple: #6f42c1;
            --accent-orange: #fd7e14;
            --accent-blue: #007bff;
        }

        /* Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: var(--text-dark);
            line-height: 1.6;
            background-color: var(--background-light);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* Header/Navbar */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2.5rem; /* Increased horizontal padding */
            background-color: var(--card-background);
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 5px var(--shadow-light); /* Slightly more visible shadow */
            z-index: 10;
        }

        .header .logo {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.5rem; /* Slightly larger logo text */
            color: var(--primary-color);
        }

        .header .logo img {
            margin-right: 12px; /* Increased margin */
            height: 45px; /* Slightly larger logo image */
            border-radius: 5px;
        }

        .header .nav-buttons {
            display: flex;
            gap: 12px; /* Increased gap between buttons */
        }

        .header .nav-buttons button {
            background-color: transparent;
            color: var(--text-dark);
            border: 1px solid var(--border-color);
            padding: 0.8rem 1.4rem; /* Increased padding for buttons */
            border-radius: 8px;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500; /* Slightly bolder text */
        }

        .header .nav-buttons button:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 3px 8px rgba(32, 178, 170, 0.3); /* More pronounced hover shadow */
        }

        .header .nav-buttons button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 3px 8px rgba(32, 178, 170, 0.4);
        }

        /* Main Dashboard Layout */
        .dashboard-container {
            display: flex;
            flex-grow: 1;
            padding: 2.5rem; /* Increased overall padding */
            gap: 2.5rem; /* Increased gap between main and sidebar */
        }

        .main-content {
            flex: 3;
            display: flex;
            flex-direction: column;
            gap: 2.5rem; /* Increased gap between sections in main content */
        }

        .sidebar {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2.5rem; /* Increased gap between sections in sidebar */
            min-width: 300px; /* Slightly larger min-width for sidebar */
        }

        /* Info Cards */
        .info-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); /* Adjusted min-width for better scaling */
            gap: 25px; /* Increased gap between info cards */
        }

        .card {
            background-color: var(--card-background);
            padding: 30px; /* Increased padding */
            border-radius: 12px;
            box-shadow: 0 5px 15px var(--shadow-light); /* Slightly more visible shadow */
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: space-between;
            min-height: 130px; /* Slightly taller cards */
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-7px); /* More pronounced lift on hover */
            box-shadow: 0 10px 25px var(--shadow-medium); /* Stronger hover shadow */
        }

        .card .icon {
            margin-bottom: 18px; /* Increased margin */
            width: 40px; /* Larger icons */
            height: 40px;
            fill: var(--primary-color);
        }

        .card .value {
            font-size: 2.5rem; /* Larger value font size */
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 8px; /* Increased margin */
        }

        .card .label {
            font-size: 1rem; /* Slightly larger label font size */
            color: var(--text-light);
        }

        /* Specific card icon colors */
        .card.logged-in .icon { fill: var(--accent-purple); }
        .card.active-cameras .icon { fill: var(--accent-orange); }
        .card.attendance .icon { fill: var(--primary-color); }
        .card.status .icon { fill: var(--status-online); }

        /* Data Sections (Tables, Live Feed) */
        .data-section {
            background-color: var(--card-background);
            padding: 30px; /* Increased padding */
            border-radius: 12px;
            box-shadow: 0 5px 15px var(--shadow-light);
        }

        .data-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px; /* Increased margin */
        }

        .data-section-header h3 {
            font-size: 1.5rem; /* Larger heading */
            color: var(--secondary-color);
            display: flex;
            align-items: center;
            gap: 12px; /* Increased gap */
            font-weight: 600; /* Bolder heading */
        }

        .data-section-header .view-all-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.7rem 1.4rem; /* Increased padding */
            border-radius: 8px;
            font-size: 0.95rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-weight: 500;
        }

        .data-section-header .view-all-btn:hover {
            background-color: #1a9c94;
        }

        .data-table {
            width: 100%;
            border-collapse: separate; /* Use separate for rounded corners on cells */
            border-spacing: 0; /* Remove default spacing */
            font-size: 1rem; /* Slightly larger font size for table data */
        }

        .data-table th, .data-table td {
            padding: 15px 20px; /* Increased padding for table cells */
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background-color: var(--background-light);
            color: var(--text-light);
            font-weight: 600; /* Bolder headers */
            text-transform: uppercase;
            font-size: 0.9rem; /* Slightly smaller uppercase headers for distinction */
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

        .data-table tbody tr:hover {
            background-color: #f0f0f0; /* Lighter hover background */
        }

        .data-table .status-tag {
            padding: 6px 12px; /* Increased padding */
            border-radius: 6px; /* Slightly more rounded */
            font-weight: 600;
            font-size: 0.85rem;
            display: inline-block;
            min-width: 60px; /* Ensure consistent width for tags */
            text-align: center;
        }

        .data-table .status-tag.in {
            background-color: rgba(40, 167, 69, 0.15);
            color: var(--status-in);
        }

        .data-table .status-tag.out {
            background-color: rgba(220, 53, 69, 0.15);
            color: var(--status-out);
        }

        .data-table a {
            color: var(--primary-color);
            text-decoration: none;
            transition: text-decoration 0.3s ease;
            font-weight: 500; /* Slightly bolder for links */
        }

        .data-table a:hover {
            text-decoration: underline;
        }

        /* Live Camera Feed */
        .live-camera-feed {
            background-color: var(--card-background);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 5px 15px var(--shadow-light);
        }

        .live-camera-feed h3 {
            font-size: 1.5rem;
            color: var(--secondary-color);
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 25px;
            font-weight: 600;
        }

        .live-camera-feed .status-indicator {
            display: flex;
            align-items: center;
            color: var(--status-online);
            font-weight: 500;
            margin-bottom: 20px; /* Increased margin */
            font-size: 1rem;
        }

        .live-camera-feed .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--status-online);
            display: inline-block;
            margin-right: 8px;
        }

        .video-placeholder {
            width: 100%;
            height: 220px; /* Slightly taller video feed */
            background-color: #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            margin-bottom: 20px; /* Increased margin */
            font-size: 1.1rem; /* Slightly larger text */
            font-weight: 500;
            border: 1px solid var(--border-color); /* Subtle border */
        }

        .camera-controls {
            display: flex;
            gap: 12px; /* Increased gap */
            margin-bottom: 20px;
        }

        .camera-controls button {
            padding: 0.8rem 1.6rem; /* Increased padding */
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-size: 0.95rem;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
            font-weight: 500;
        }

        .camera-controls .view-btn {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 3px 8px rgba(32, 178, 170, 0.3);
        }
        .camera-controls .view-btn:hover {
            background-color: #1a9c94;
            box-shadow: 0 5px 12px rgba(32, 178, 170, 0.4);
        }

        .camera-controls .stop-btn {
            background-color: var(--status-offline);
            color: white;
            box-shadow: 0 3px 8px rgba(220, 53, 69, 0.3);
        }
        .camera-controls .stop-btn:hover {
            background-color: #c82333;
            box-shadow: 0 5px 12px rgba(220, 53, 69, 0.4);
        }

        /* Active Cameras List */
        .active-cameras-list .camera-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0; /* Increased vertical padding */
            border-bottom: 1px solid var(--border-color);
        }

        .active-cameras-list .camera-item:last-child {
            border-bottom: none;
        }

        .active-cameras-list .camera-item span {
            font-weight: 500;
            font-size: 1rem; /* Slightly larger text */
        }

        /* SVG Icons */
        .icon-small {
            width: 20px;
            height: 20px;
            fill: currentColor; /* Inherit color from parent */
        }
        .icon-medium {
            width: 26px; /* Slightly larger icons */
            height: 26px;
            fill: currentColor;
        }
        .icon-large {
            width: 35px; /* Larger icons */
            height: 35px;
            fill: currentColor;
        }

        /* Responsive Adjustments */
        @media (max-width: 1200px) {
            .dashboard-container {
                flex-direction: column; /* Stack main content and sidebar */
            }
            .sidebar {
                min-width: unset; /* Remove min-width when stacked */
            }
        }

        @media (max-width: 992px) {
            .header {
                padding: 1rem 1.5rem;
            }
            .header .logo {
                font-size: 1.3rem;
            }
            .header .logo img {
                height: 35px;
            }
            .header .nav-buttons button {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }
            .dashboard-container {
                padding: 2rem;
                gap: 2rem;
            }
            .main-content, .sidebar {
                gap: 2rem;
            }
            .info-cards {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 20px;
            }
            .card {
                padding: 25px;
                min-height: 120px;
            }
            .card .value {
                font-size: 2.2rem;
            }
            .card .label {
                font-size: 0.9rem;
            }
            .data-section {
                padding: 20px;
            }
            .data-section-header h3 {
                font-size: 1.3rem;
            }
            .data-table th, .data-table td {
                padding: 12px 15px;
                font-size: 0.9rem;
            }
            .live-camera-feed .video-placeholder {
                height: 200px;
            }
            .camera-controls button {
                padding: 0.7rem 1.4rem;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                padding: 1rem;
            }
            .header .nav-buttons {
                margin-top: 1rem;
                flex-wrap: wrap;
                justify-content: center;
            }
            .dashboard-container {
                padding: 1.5rem;
                gap: 1.5rem;
            }
            .info-cards {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            .card .value {
                font-size: 1.8rem;
            }
            .card .label {
                font-size: 0.85rem;
            }
            .data-section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            .data-section-header .view-all-btn {
                width: 100%;
                text-align: center;
            }
            .data-table th, .data-table td {
                padding: 10px;
                font-size: 0.85rem;
            }
            .live-camera-feed .video-placeholder {
                height: 180px;
            }
        }

        @media (max-width: 480px) {
            .header .logo {
                font-size: 1.2rem;
            }
            .header .logo img {
                height: 30px;
            }
            .header .nav-buttons button {
                padding: 0.6rem 0.9rem;
                font-size: 0.8rem;
            }
            .dashboard-container {
                padding: 1rem;
                gap: 1rem;
            }
            .info-cards {
                grid-template-columns: 1fr; /* Stack cards vertically */
            }
            .card {
                padding: 20px;
                min-height: 100px;
            }
            .card .value {
                font-size: 1.6rem;
            }
            .card .label {
                font-size: 0.8rem;
            }
            .data-section {
                padding: 15px;
            }
            .data-section-header h3 {
                font-size: 1.2rem;
            }
            .data-table th, .data-table td {
                padding: 8px;
                font-size: 0.75rem;
            }
            .live-camera-feed .video-placeholder {
                height: 150px;
            }
            .camera-controls button {
                padding: 0.6rem 1rem;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>

    <header class="header">
        <div class="logo">
            <img src="https://placehold.co/40x40/20B2AA/FFFFFF?text=VE" alt="Vigilant Eye Logo">
            <span>Face Attendance System</span>
        </div>
        <div class="nav-buttons">
            <button class="active">Dashboard</button>
            <button>Register User</button>
            <button>Manage Cameras</button>
            <button>Attendance Logs</button>
            <button>Users</button>
            <button>Logout</button>
        </div>
    </header>

    <div class="dashboard-container">
        <div class="main-content">
            <!-- Info Cards Section -->
            <section class="info-cards">
                <div class="card logged-in">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                    <div class="value">2</div>
                    <div class="label">Currently Logged In</div>
                </div>
                <div class="card active-cameras">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
                    </svg>
                    <div class="value">1</div>
                    <div class="label">Active Cameras</div>
                </div>
                <div class="card attendance">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                    </svg>
                    <div class="value">3</div>
                    <div class="label">Today's Attendance</div>
                </div>
                <div class="card status">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                    </svg>
                    <div class="value">Online</div>
                    <div class="label">System Status</div>
                </div>
            </section>

            <!-- Currently Logged In Table - Enhanced for Professionalism -->
            <section class="data-section currently-logged-in-table">
                <div class="data-section-header">
                    <h3>
                        <svg class="icon-medium" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                        Currently Logged In
                    </h3>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Login Time</th>
                            <th>Camera</th>
                            <th>Type</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><a href="#">Reshma</a></td>
                            <td>10:18:09</td>
                            <td>Webcam</td>
                            <td><span class="status-tag in">IN</span></td>
                        </tr>
                        <tr>
                            <td><a href="#">Vishnu</a></td>
                            <td>10:18:23</td>
                            <td>Webcam</td>
                            <td><span class="status-tag in">IN</span></td>
                        </tr>
                        <!-- Add more rows as needed -->
                        <tr>
                            <td><a href="#">Priya</a></td>
                            <td>10:20:15</td>
                            <td>Front Door Cam</td>
                            <td><span class="status-tag in">IN</span></td>
                        </tr>
                        <tr>
                            <td><a href="#">Arjun</a></td>
                            <td>10:25:00</td>
                            <td>Office Cam 1</td>
                            <td><span class="status-tag in">IN</span></td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Recent Attendance Logs Table -->
            <section class="data-section">
                <div class="data-section-header">
                    <h3>
                        <svg class="icon-medium" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                        </svg>
                        Recent Attendance Logs
                    </h3>
                    <button class="view-all-btn">View All</button>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Date</th>
                            <th>Login</th>
                            <th>Logout</th>
                            <th>Duration</th>
                            <th>Camera</th>
                            <th>Type</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><a href="#">Vishnu</a></td>
                            <td>2025-07-31</td>
                            <td>10:18:23</td>
                            <td>-</td>
                            <td>-</td>
                            <td>Webcam</td>
                            <td><span class="status-tag in">IN</span></td>
                        </tr>
                        <tr>
                            <td><a href="#">Reshma</a></td>
                            <td>2025-07-31</td>
                            <td>09:59:42</td>
                            <td>10:05:00</td>
                            <td>00:05</td>
                            <td>Webcam</td>
                            <td><span class="status-tag out">OUT</span></td>
                        </tr>
                        <tr>
                            <td><a href="#">Priya</a></td>
                            <td>2025-07-31</td>
                            <td>09:30:00</td>
                            <td>09:45:00</td>
                            <td>00:15</td>
                            <td>Main Entrance</td>
                            <td><span class="status-tag out">OUT</span></td>
                        </tr>
                        <tr>
                            <td><a href="#">Arjun</a></td>
                            <td>2025-07-31</td>
                            <td>09:00:00</td>
                            <td>-</td>
                            <td>-</td>
                            <td>Office Cam 1</td>
                            <td><span class="status-tag in">IN</span></td>
                        </tr>
                    </tbody>
                </table>
            </section>
        </div>

        <div class="sidebar">
            <!-- Live Camera Feed Section -->
            <section class="live-camera-feed">
                <h3>
                    <svg class="icon-medium" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
                    </svg>
                    Live Camera Feed
                </h3>
                <p class="status-indicator"><span class="status-dot"></span> Online</p>
                <div class="video-placeholder">
                    Front Camera (IN) - Live Stream
                </div>
                <div class="camera-controls">
                    <button class="view-btn">View</button>
                    <button class="stop-btn">Stop</button>
                </div>
            </section>

            <!-- Active Cameras List -->
            <section class="data-section active-cameras-list">
                <div class="data-section-header">
                    <h3>
                        <svg class="icon-medium" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
                        </svg>
                        Active Cameras
                    </h3>
                </div>
                <div class="camera-item">
                    <span>Webcam (IN)</span>
                    <div>
                        <button class="view-btn">View</button>
                        <button class="stop-btn">Stop</button>
                    </div>
                </div>
                <div class="camera-item">
                    <span>Office Cam 1</span>
                    <div>
                        <button class="view-btn">View</button>
                        <button class="stop-btn">Stop</button>
                    </div>
                </div>
                <div class="camera-item">
                    <span>Main Entrance</span>
                    <div>
                        <button class="view-btn">View</button>
                        <button class="stop-btn">Stop</button>
                    </div>
                </div>
            </section>
        </div>
    </div>

</body>
</html>
