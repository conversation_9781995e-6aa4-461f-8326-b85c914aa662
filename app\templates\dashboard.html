<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VigilantEye - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        body {
            background-color: var(--light-bg);
            color: #1e293b;
            line-height: 1.6;
        }

        /* Modern Navigation */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
            border: none;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            text-decoration: none;
        }

        .navbar-brand i {
            margin-right: 0.5rem;
            color: #60a5fa;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            padding: 0.75rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.25rem;
            transition: var(--transition);
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white !important;
            transform: translateY(-1px);
        }

        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.15) !important;
            color: white !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .nav-link i {
            margin-right: 0.5rem;
            width: 16px;
            text-align: center;
        }

        /* Modern Cards */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            background: white;
        }

        .card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            padding: 1.5rem;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 600;
            color: #1e293b;
            font-size: 1.1rem;
        }

        .card-header i {
            color: var(--primary-color);
            margin-right: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Stats Cards */
        .card-stats {
            background: white;
            color: #1e293b;
            border-left: 4px solid var(--primary-color);
            transition: var(--transition);
        }

        .card-stats:nth-child(1) { border-left-color: var(--primary-color); }
        .card-stats:nth-child(2) { border-left-color: var(--success-color); }
        .card-stats:nth-child(3) { border-left-color: var(--warning-color); }
        .card-stats:nth-child(4) { border-left-color: var(--danger-color); }

        .card-stats .card-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .card-stats h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0;
            line-height: 1;
        }

        .card-stats .fa-2x {
            color: var(--secondary-color);
            opacity: 0.6;
        }

        .card-stats:nth-child(1) .fa-2x { color: var(--primary-color); }
        .card-stats:nth-child(2) .fa-2x { color: var(--success-color); }
        .card-stats:nth-child(3) .fa-2x { color: var(--warning-color); }
        .card-stats:nth-child(4) .fa-2x { color: var(--danger-color); }

        /* Page Title */
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 2rem;
        }

        .page-title i {
            color: var(--primary-color);
            margin-right: 0.75rem;
        }
        /* Camera Styles */
        .camera-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            box-shadow: 0 0 0 2px white, 0 0 0 3px currentColor;
        }

        .camera-active {
            background-color: var(--success-color);
            color: var(--success-color);
        }

        .camera-inactive {
            background-color: var(--danger-color);
            color: var(--danger-color);
        }

        .camera-feed {
            width: 100%;
            height: 220px;
            object-fit: cover;
            border-radius: var(--border-radius);
            border: 2px solid #e2e8f0;
            transition: var(--transition);
        }

        .camera-feed:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .camera-feed-container {
            position: relative;
            margin-bottom: 1rem;
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .camera-label {
            position: absolute;
            top: 12px;
            left: 12px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            backdrop-filter: blur(4px);
        }

        .no-camera-placeholder {
            width: 100%;
            height: 220px;
            background: #f1f5f9;
            border: 2px dashed #cbd5e1;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: var(--secondary-color);
        }

        /* Tables */
        .table {
            margin-bottom: 0;
        }

        .table th {
            border-top: none;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
            color: #475569;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 1rem 0.75rem;
        }

        .table td {
            border-top: 1px solid #f1f5f9;
            padding: 1rem 0.75rem;
            vertical-align: middle;
        }

        .table-striped > tbody > tr:nth-of-type(odd) > td {
            background-color: #f8fafc;
        }

        /* Badges */
        .badge {
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: var(--transition);
            border: none;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }

        .btn-outline-primary {
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-danger {
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
            background: transparent;
        }

        .btn-outline-danger:hover {
            background-color: var(--danger-color);
            color: white;
            transform: translateY(-1px);
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        /* Links */
        .text-decoration-none {
            color: var(--primary-color);
            font-weight: 500;
            transition: var(--transition);
        }

        .text-decoration-none:hover {
            color: var(--primary-dark);
            text-decoration: underline !important;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .card-stats h2 {
                font-size: 2rem;
            }

            .page-title {
                font-size: 1.75rem;
            }

            .camera-feed {
                height: 180px;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-eye"></i> VigilantEye
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link active" href="/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="/register-user">
                    <i class="fas fa-user-plus"></i> Register User
                </a>
                <a class="nav-link" href="/manage-camera">
                    <i class="fas fa-video"></i> Manage Cameras
                </a>
                <a class="nav-link" href="/attendance">
                    <i class="fas fa-clock"></i> Attendance Logs
                </a>
                <a class="nav-link" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
                <div class="navbar-nav ms-3">
                    <form method="post" action="/logout" style="margin: 0;">
                        <button type="submit" class="nav-link btn btn-link text-light border-0" style="background: none; color: rgba(255, 255, 255, 0.9) !important; font-weight: 500; padding: 0.75rem 1rem; border-radius: 8px; margin: 0 0.25rem; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="page-title">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </h1>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Currently Logged In</h5>
                                <h2>{{ current_users|length }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Active Cameras</h5>
                                <h2>{{ active_cameras|length }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-video fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Today's Attendance</h5>
                                <h2 id="today-attendance-count">{{ today_total }}</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">System Status</h5>
                                <h6 class="text-success">
                                    <i class="fas fa-check-circle"></i> Online
                                </h6>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-server fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Camera Feeds -->
        {% if active_cameras %}
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-video"></i> Live Camera Feeds</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for camera in active_cameras %}
                            <div class="col-md-4 col-lg-3 mb-3">
                                <div class="camera-feed-container">
                                    <img src="/camera-stream/{{ camera.camera_id }}"
                                         alt="{{ camera.camera_name }}"
                                         class="camera-feed"
                                         data-camera-id="{{ camera.camera_id }}"
                                         onload="this.style.display='block'; this.nextElementSibling.style.display='none';"
                                         onerror="handleCameraError(this);">
                                    <div class="no-camera-placeholder" style="display: none;">
                                        <i class="fas fa-video-slash fa-2x mb-2"></i>
                                        <small>Camera Offline</small>
                                    </div>
                                    <div class="camera-label">
                                        {{ camera.camera_name }} ({{ camera.camera_type }})
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Currently Logged In Users -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-check"></i> Currently Logged In</h5>
                    </div>
                    <div class="card-body" id="current-users-list">
                        {% if current_users %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Login Time</th>
                                            <th>Camera</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody id="current-users-tbody">
                                        {% for user in current_users %}
                                        <tr>
                                            <td>
                                                <a href="/user/{{ user.user_id }}" class="text-decoration-none">
                                                    {{ user.name }}
                                                </a>
                                            </td>
                                            <td>{{ user.login_time }}</td>
                                            <td>{{ user.camera_name }}</td>
                                            <td>
                                                <span class="badge bg-{% if user.camera_type == 'IN' %}success{% else %}warning{% endif %}">
                                                    {{ user.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">No users currently logged in.</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Active Cameras -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-video"></i> Active Cameras</h5>
                    </div>
                    <div class="card-body">
                        {% if active_cameras %}
                            {% for camera in active_cameras %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <span class="camera-status {% if camera.is_active %}camera-active{% else %}camera-inactive{% endif %}"></span>
                                    <strong>{{ camera.camera_name }}</strong>
                                    <small class="text-muted">({{ camera.camera_type }})</small>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewCamera({{ camera.camera_id }})">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="stopCamera({{ camera.camera_id }})">
                                        <i class="fas fa-stop"></i> Stop
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No active cameras.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Attendance Logs -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-history"></i> Recent Attendance Logs </h5>
                        <a href="/attendance" class="btn btn-sm btn-primary">View All</a>
                    </div>
                    <div class="card-body">
                        {% if today_last_5 %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Date</th>
                                            <th>Login</th>
                                            <th>Logout</th>
                                            <th>Duration</th>
                                            <th>Camera</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-attendance-tbody">
                                        {% for log in today_last_5 %}
                                        <tr>
                                            <td>
                                                <a href="/user/{{ log.user_id }}" class="text-decoration-none">
                                                    {{ log.name }}
                                                </a>
                                            </td>
                                            <td>{{ log.date }}</td>
                                            <td>{{ log.login_time or '-' }}</td>
                                            <td>{{ log.logout_time or '-' }}</td>
                                            <td>{{ log.duration or '-' }}</td>
                                            <td>{{ log.camera_name }}</td>
                                            <td>
                                                <span class="badge bg-{% if log.camera_type == 'IN' %}success{% else %}warning{% endif %}">
                                                    {{ log.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted">No attendance logs for today.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCamera(cameraId) {
            window.open(`/camera-stream/${cameraId}`, '_blank', 'width=800,height=600');
        }

        function stopCamera(cameraId) {
            if (confirm('Are you sure you want to stop this camera?')) {
                fetch(`/stop-camera/${cameraId}`, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message || data.error);
                        location.reload();
                    })
                    .catch(error => {
                        alert('Error stopping camera');
                        console.error(error);
                    });
            }
        }

        // Handle camera feed errors
        function handleCameraError(img) {
            console.log('Camera feed error for camera:', img.dataset.cameraId);
            img.style.display = 'none';
            img.nextElementSibling.style.display = 'flex';

            // Try to reconnect after 3 seconds
            setTimeout(() => {
                const cameraId = img.dataset.cameraId;
                const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                img.src = newSrc;
            }, 3000);
        }

        // Initialize camera feeds with proper error handling
        function initializeCameraFeeds() {
            const cameraFeeds = document.querySelectorAll('.camera-feed');
            cameraFeeds.forEach(feed => {
                // Add timestamp to prevent caching
                const cameraId = feed.dataset.cameraId;
                feed.src = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;

                // Set up periodic refresh for each feed
                setInterval(() => {
                    if (feed.style.display !== 'none') {
                        const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                        feed.src = newSrc;
                    }
                }, 100); // Refresh every 100ms for smooth video
            });
        }

        // Initialize feeds when page loads
        document.addEventListener('DOMContentLoaded', initializeCameraFeeds);

        // Real-time dashboard updates
        function updateDashboardData() {
            // Update recent attendance logs
            fetch('/api/attendance/recent')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateRecentAttendanceLogs(data.logs, data.today_total);
                    }
                })
                .catch(error => console.error('Error fetching recent attendance:', error));

            // Update currently logged in users
            fetch('/api/attendance/current')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateCurrentlyLoggedIn(data.users);
                    }
                })
                .catch(error => console.error('Error fetching current users:', error));
        }

        function updateRecentAttendanceLogs(logs, todayTotal) {
            // Update recent attendance table
            const recentTbody = document.querySelector('#recent-attendance-tbody');
            if (recentTbody) {
                recentTbody.innerHTML = '';
                logs.forEach(log => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><a href="/user/${log.user_id}" class="text-decoration-none">${log.name}</a></td>
                        <td>${log.date}</td>
                        <td>${log.login_time || '-'}</td>
                        <td>${log.logout_time || '-'}</td>
                        <td>${log.duration || '-'}</td>
                        <td>${log.camera_name}</td>
                        <td>
                            <span class="badge ${log.camera_type === 'IN' ? 'bg-success' : 'bg-warning'}">
                                ${log.camera_type}
                            </span>
                        </td>
                    `;
                    recentTbody.appendChild(row);
                });
            }

            // Update today's attendance count in stats card using the correct selector
            const todayCountElement = document.querySelector('#today-attendance-count');
            if (todayCountElement && todayTotal !== undefined) {
                todayCountElement.textContent = todayTotal;
            }
        }

        function updateCurrentlyLoggedIn(users) {
            const currentUsersContainer = document.querySelector('#current-users-list');
            const currentUsersCount = document.querySelector('.card-stats:nth-child(1) h2');
            const currentUsersTbody = document.querySelector('#current-users-tbody');

            if (currentUsersCount) {
                currentUsersCount.textContent = users.length;
            }

            if (currentUsersContainer) {
                if (users.length === 0) {
                    currentUsersContainer.innerHTML = '<p class="text-muted">No users currently logged in.</p>';
                } else {
                    // Update the table structure
                    currentUsersContainer.innerHTML = `
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Login Time</th>
                                        <th>Camera</th>
                                        <th>Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${users.map(user => `
                                        <tr>
                                            <td>
                                                <a href="/user/${user.user_id}" class="text-decoration-none">
                                                    ${user.name}
                                                </a>
                                            </td>
                                            <td>${user.login_time}</td>
                                            <td>${user.camera_name}</td>
                                            <td>
                                                <span class="badge bg-${user.camera_type === 'IN' ? 'success' : 'warning'}">
                                                    ${user.camera_type}
                                                </span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                }
            }
        }

        // Update dashboard data every 3 seconds for real-time updates
        setInterval(updateDashboardData, 3000);

        // Initial update when page loads
        document.addEventListener('DOMContentLoaded', () => {
            updateDashboardData();
        });

        // Full page refresh every 5 minutes as backup
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
