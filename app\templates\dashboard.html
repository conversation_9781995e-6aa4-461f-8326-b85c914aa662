<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Attendance System - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Root variables for consistent theming */
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --white: #ffffff;
            --border-radius: 8px;
            --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            --box-shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* Body and container styling */
        body {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            padding: 1.5rem 0;
        }

        /* Professional card styling */
        .card {
            border: 1px solid #e3e6f0;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: all 0.3s ease;
            background: var(--white);
        }

        .card:hover {
            box-shadow: var(--box-shadow-hover);
        }

        .card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #e3e6f0;
            padding: 1rem 1.25rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.25rem;
        }

        /* Stats cards styling */
        .card-stats {
            background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
            color: white;
            border: none;
            height: 100%;
        }

        .card-stats .card-body {
            padding: 1.5rem;
        }

        .card-stats h5 {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            opacity: 0.9;
        }

        .card-stats h2 {
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
        }

        /* Camera feed styling - compact for corner placement */
        .camera-feed-compact {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-radius: var(--border-radius);
            border: 2px solid #e3e6f0;
            transition: border-color 0.3s ease;
        }

        .camera-feed-compact:hover {
            border-color: var(--primary-color);
        }

        .camera-feed-container-compact {
            position: relative;
            margin-bottom: 1rem;
        }

        .camera-label {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .no-camera-placeholder {
            width: 100%;
            height: 180px;
            background: var(--light-color);
            border: 2px dashed #dee2e6;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: var(--secondary-color);
        }

        /* Camera status indicators */
        .camera-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .camera-active {
            background-color: var(--success-color);
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
        }

        .camera-inactive {
            background-color: var(--danger-color);
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
        }

        /* Camera sidebar styling */
        .camera-sidebar {
            position: sticky;
            top: 1.5rem;
        }

        .camera-item {
            padding: 0.75rem;
            border: 1px solid #e3e6f0;
            border-radius: var(--border-radius);
            margin-bottom: 0.5rem;
            background: var(--white);
            transition: all 0.3s ease;
        }

        .camera-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .camera-item:last-child {
            margin-bottom: 0;
        }

        /* Main content area styling */
        .main-content {
            padding-right: 1rem;
        }

        /* Table styling */
        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--dark-color);
            background-color: #f8f9fa;
        }

        .table td {
            vertical-align: middle;
        }

        /* Button styling */
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        /* Badge styling */
        .badge {
            font-size: 0.7rem;
            padding: 0.35em 0.65em;
        }

        /* Navbar styling */
        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            color: #fff !important;
        }

        .navbar-nav .nav-link.active:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }

        /* Status indicator */
        .status-indicator {
            font-size: 0.75rem;
            color: var(--success-color);
        }

        /* Empty state styling */
        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--secondary-color);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Responsive adjustments */
        @media (max-width: 991.98px) {
            .camera-sidebar {
                position: static;
                margin-top: 1rem;
            }

            .main-content {
                padding-right: 0;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-eye"></i> VigilantEye
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link active fw-bold" href="/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="/register-user">
                    <i class="fas fa-user-plus"></i> Register User
                </a>
                <a class="nav-link" href="/manage-camera">
                    <i class="fas fa-video"></i> Manage Cameras
                </a>
                <a class="nav-link" href="/attendance">
                    <i class="fas fa-clock"></i> Attendance Logs
                </a>
                <a class="nav-link" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
                <div class="navbar-nav ms-3">
                    <form method="post" action="/logout" style="margin: 0;">
                        <button type="submit" class="nav-link btn btn-link text-light border-0" style="background: none;">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="mb-0">
                    <i class="fas fa-tachometer-alt text-primary"></i> Face Attendance System
                </h1>
                <p class="text-muted mb-0">Dashboard</p>
            </div>
        </div>

        <!-- Stats Cards Row -->
        <div class="row g-3 mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5>Currently Logged In</h5>
                                <h2>{{ current_users|length }}</h2>
                            </div>
                            <div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5>Active Cameras</h5>
                                <h2>{{ active_cameras|length }}</h2>
                            </div>
                            <div>
                                <i class="fas fa-video fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5>Today's Attendance</h5>
                                <h2 id="today-attendance-count">{{ today_total }}</h2>
                            </div>
                            <div>
                                <i class="fas fa-clock fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5>System Status</h5>
                                <h6 class="mb-0">
                                    <i class="fas fa-circle text-success"></i> Online
                                </h6>
                            </div>
                            <div>
                                <i class="fas fa-server fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Layout -->
        <div class="row g-3">
            <!-- Main Content Area - Currently Logged In Users (Primary Focus) -->
            <div class="col-lg-8 main-content">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user-check text-primary"></i> Currently Logged In</h5>
                    </div>
                    <div class="card-body" id="current-users-list">
                        {% if current_users %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Login Time</th>
                                            <th>Camera</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody id="current-users-tbody">
                                        {% for user in current_users %}
                                        <tr>
                                            <td>
                                                <a href="/user/{{ user.user_id }}" class="text-decoration-none fw-semibold">
                                                    {{ user.name }}
                                                </a>
                                            </td>
                                            <td>{{ user.login_time }}</td>
                                            <td>{{ user.camera_name }}</td>
                                            <td>
                                                <span class="badge bg-{% if user.camera_type == 'IN' %}success{% else %}warning{% endif %}">
                                                    {{ user.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="empty-state">
                                <i class="fas fa-users"></i>
                                <p class="mb-0">No users currently logged in.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Right Sidebar - Camera Components -->
            <div class="col-lg-4">
                <div class="camera-sidebar">
                    <!-- Live Camera Feed -->
                    <div class="card mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-video text-primary"></i> Live Camera Feed</h6>
                            <span class="status-indicator">
                                <i class="fas fa-circle"></i> Online
                            </span>
                        </div>
                        <div class="card-body">
                            {% if active_cameras %}
                                <div class="camera-feed-container-compact">
                                    <img src="/camera-stream/{{ active_cameras[0].camera_id }}"
                                         alt="{{ active_cameras[0].camera_name }}"
                                         class="camera-feed-compact"
                                         data-camera-id="{{ active_cameras[0].camera_id }}"
                                         onload="this.style.display='block'; this.nextElementSibling.style.display='none';"
                                         onerror="handleCameraError(this);">
                                    <div class="no-camera-placeholder" style="display: none;">
                                        <i class="fas fa-video-slash fa-2x mb-2"></i>
                                        <small>Camera Offline</small>
                                    </div>
                                    <div class="camera-label">
                                        {{ active_cameras[0].camera_name }} ({{ active_cameras[0].camera_type }})
                                    </div>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-outline-primary btn-sm flex-fill">
                                        <i class="fas fa-expand"></i> View
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm flex-fill">
                                        <i class="fas fa-stop"></i> Stop
                                    </button>
                                </div>
                            {% else %}
                                <div class="no-camera-placeholder">
                                    <i class="fas fa-video-slash fa-2x mb-2"></i>
                                    <small>No active cameras</small>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Active Cameras List -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-video text-primary"></i> Active Cameras</h6>
                        </div>
                        <div class="card-body">
                            {% if active_cameras %}
                                {% for camera in active_cameras %}
                                <div class="camera-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="camera-status {% if camera.is_active %}camera-active{% else %}camera-inactive{% endif %}"></span>
                                            <strong style="font-size: 0.875rem;">{{ camera.camera_name }}</strong>
                                            <br>
                                            <small class="text-muted">({{ camera.camera_type }})</small>
                                        </div>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewCamera({{ camera.camera_id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="stopCamera({{ camera.camera_id }})">
                                                <i class="fas fa-stop"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="empty-state">
                                    <i class="fas fa-video-slash"></i>
                                    <p class="mb-0">No active cameras.</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Attendance Logs -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-history text-primary"></i> Recent Attendance Logs</h5>
                        <a href="/attendance" class="btn btn-sm btn-primary">
                            <i class="fas fa-external-link-alt me-1"></i> View All
                        </a>
                    </div>
                    <div class="card-body">
                        {% if today_last_5 %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Date</th>
                                            <th>Login</th>
                                            <th>Logout</th>
                                            <th>Duration</th>
                                            <th>Camera</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-attendance-tbody">
                                        {% for log in today_last_5 %}
                                        <tr>
                                            <td>
                                                <a href="/user/{{ log.user_id }}" class="text-decoration-none fw-semibold">
                                                    {{ log.name }}
                                                </a>
                                            </td>
                                            <td>{{ log.date }}</td>
                                            <td>{{ log.login_time or '-' }}</td>
                                            <td>{{ log.logout_time or '-' }}</td>
                                            <td>{{ log.duration or '-' }}</td>
                                            <td>{{ log.camera_name }}</td>
                                            <td>
                                                <span class="badge bg-{% if log.camera_type == 'IN' %}success{% else %}warning{% endif %}">
                                                    {{ log.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="empty-state">
                                <i class="fas fa-clock"></i>
                                <p class="mb-0">No attendance logs for today.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCamera(cameraId) {
            window.open(`/camera-stream/${cameraId}`, '_blank', 'width=800,height=600');
        }

        function stopCamera(cameraId) {
            if (confirm('Are you sure you want to stop this camera?')) {
                fetch(`/stop-camera/${cameraId}`, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message || data.error);
                        location.reload();
                    })
                    .catch(error => {
                        alert('Error stopping camera');
                        console.error(error);
                    });
            }
        }

        // Handle camera feed errors
        function handleCameraError(img) {
            console.log('Camera feed error for camera:', img.dataset.cameraId);
            img.style.display = 'none';
            img.nextElementSibling.style.display = 'flex';

            // Try to reconnect after 3 seconds
            setTimeout(() => {
                const cameraId = img.dataset.cameraId;
                const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                img.src = newSrc;
            }, 3000);
        }

        // Initialize camera feeds with proper error handling
        function initializeCameraFeeds() {
            const cameraFeeds = document.querySelectorAll('.camera-feed');
            cameraFeeds.forEach(feed => {
                // Add timestamp to prevent caching
                const cameraId = feed.dataset.cameraId;
                feed.src = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;

                // Set up periodic refresh for each feed
                setInterval(() => {
                    if (feed.style.display !== 'none') {
                        const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                        feed.src = newSrc;
                    }
                }, 100); // Refresh every 100ms for smooth video
            });
        }

        // Initialize feeds when page loads
        document.addEventListener('DOMContentLoaded', initializeCameraFeeds);

        // Real-time dashboard updates
        function updateDashboardData() {
            // Update recent attendance logs
            fetch('/api/attendance/recent')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateRecentAttendanceLogs(data.logs, data.today_total);
                    }
                })
                .catch(error => console.error('Error fetching recent attendance:', error));

            // Update currently logged in users
            fetch('/api/attendance/current')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateCurrentlyLoggedIn(data.users);
                    }
                })
                .catch(error => console.error('Error fetching current users:', error));
        }

        function updateRecentAttendanceLogs(logs, todayTotal) {
            // Update recent attendance table
            const recentTbody = document.querySelector('#recent-attendance-tbody');
            if (recentTbody) {
                recentTbody.innerHTML = '';
                logs.forEach(log => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><a href="/user/${log.user_id}" class="text-decoration-none">${log.name}</a></td>
                        <td>${log.date}</td>
                        <td>${log.login_time || '-'}</td>
                        <td>${log.logout_time || '-'}</td>
                        <td>${log.duration || '-'}</td>
                        <td>${log.camera_name}</td>
                        <td>
                            <span class="badge ${log.camera_type === 'IN' ? 'bg-success' : 'bg-warning'}">
                                ${log.camera_type}
                            </span>
                        </td>
                    `;
                    recentTbody.appendChild(row);
                });
            }

            // Update today's attendance count in stats card using the correct selector
            const todayCountElement = document.querySelector('#today-attendance-count');
            if (todayCountElement && todayTotal !== undefined) {
                todayCountElement.textContent = todayTotal;
            }
        }

        function updateCurrentlyLoggedIn(users) {
            const currentUsersContainer = document.querySelector('#current-users-list');
            const currentUsersCount = document.querySelector('.card-stats:nth-child(1) h2');
            const currentUsersTbody = document.querySelector('#current-users-tbody');

            if (currentUsersCount) {
                currentUsersCount.textContent = users.length;
            }

            if (currentUsersContainer) {
                if (users.length === 0) {
                    currentUsersContainer.innerHTML = '<p class="text-muted">No users currently logged in.</p>';
                } else {
                    // Update the table structure
                    currentUsersContainer.innerHTML = `
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Login Time</th>
                                        <th>Camera</th>
                                        <th>Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${users.map(user => `
                                        <tr>
                                            <td>
                                                <a href="/user/${user.user_id}" class="text-decoration-none">
                                                    ${user.name}
                                                </a>
                                            </td>
                                            <td>${user.login_time}</td>
                                            <td>${user.camera_name}</td>
                                            <td>
                                                <span class="badge bg-${user.camera_type === 'IN' ? 'success' : 'warning'}">
                                                    ${user.camera_type}
                                                </span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                }
            }
        }

        // Update dashboard data every 3 seconds for real-time updates
        setInterval(updateDashboardData, 3000);

        // Initial update when page loads
        document.addEventListener('DOMContentLoaded', () => {
            updateDashboardData();
        });

        // Full page refresh every 5 minutes as backup
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
