<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VigilantEye - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #007bff;
            --primary-dark: #0056b3;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --white: #ffffff;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
            --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --card-shadow-hover: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --border-radius: 0.375rem;
            --border-radius-lg: 0.5rem;
            --transition: all 0.15s ease-in-out;
        }

        * {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: var(--light-bg);
            color: var(--gray-900);
            line-height: 1.5;
            font-size: 0.875rem;
        }

        /* Professional Navigation */
        .navbar {
            background-color: var(--white) !important;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
            color: var(--gray-900) !important;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .navbar-brand i {
            margin-right: 0.5rem;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .nav-link {
            color: var(--gray-700) !important;
            font-weight: 500;
            padding: 0.5rem 0.75rem !important;
            border-radius: var(--border-radius);
            margin: 0 0.125rem;
            transition: var(--transition);
            font-size: 0.875rem;
        }

        .nav-link:hover {
            background-color: var(--gray-100);
            color: var(--primary-color) !important;
        }

        .nav-link.active {
            background-color: var(--primary-color) !important;
            color: var(--white) !important;
        }

        .nav-link i {
            margin-right: 0.375rem;
            width: 14px;
            text-align: center;
            font-size: 0.875rem;
        }

        /* Professional Cards */
        .card {
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            background: var(--white);
        }

        .card:hover {
            box-shadow: var(--card-shadow-hover);
        }

        .card-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            padding: 1rem 1.25rem;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 600;
            color: var(--gray-900);
            font-size: 1rem;
        }

        .card-header i {
            color: var(--gray-600);
            margin-right: 0.5rem;
            font-size: 0.875rem;
        }

        .card-body {
            padding: 1.25rem;
        }

        /* Stats Cards */
        .card-stats {
            background: var(--white);
            color: var(--gray-900);
            border: 1px solid var(--gray-200);
            transition: var(--transition);
        }

        .card-stats .card-title {
            font-size: 0.75rem;
            font-weight: 500;
            color: var(--gray-600);
            margin-bottom: 0.25rem;
            text-transform: none;
            letter-spacing: normal;
        }

        .card-stats h2 {
            font-size: 2rem;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
            line-height: 1.2;
        }

        .card-stats .fa-2x {
            color: var(--gray-400);
            font-size: 1.5rem;
        }

        .card-stats:nth-child(1) .fa-2x { color: var(--primary-color); }
        .card-stats:nth-child(2) .fa-2x { color: var(--success-color); }
        .card-stats:nth-child(3) .fa-2x { color: var(--warning-color); }
        .card-stats:nth-child(4) .fa-2x { color: var(--success-color); }

        /* Page Title */
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 1.5rem;
        }

        .page-title i {
            color: var(--primary-color);
            margin-right: 0.5rem;
            font-size: 1.25rem;
        }
        /* Camera Styles */
        .camera-status {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }

        .camera-active {
            background-color: var(--success-color);
        }

        .camera-inactive {
            background-color: var(--danger-color);
        }

        .camera-feed {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-200);
            transition: var(--transition);
            background-color: var(--gray-800);
        }

        .camera-feed:hover {
            border-color: var(--primary-color);
        }

        .camera-feed-container {
            position: relative;
            margin-bottom: 1rem;
            border-radius: var(--border-radius);
            overflow: hidden;
            background-color: var(--gray-800);
        }

        .camera-label {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(0, 0, 0, 0.75);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .no-camera-placeholder {
            width: 100%;
            height: 200px;
            background: var(--gray-100);
            border: 1px dashed var(--gray-300);
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: var(--gray-500);
        }

        /* Tables */
        .table {
            margin-bottom: 0;
            font-size: 0.875rem;
        }

        .table th {
            border-top: none;
            border-bottom: 1px solid var(--gray-200);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.75rem;
            text-transform: none;
            letter-spacing: normal;
            padding: 0.75rem;
            background-color: var(--gray-50);
        }

        .table td {
            border-top: 1px solid var(--gray-200);
            padding: 0.75rem;
            vertical-align: middle;
            color: var(--gray-900);
        }

        .table-striped > tbody > tr:nth-of-type(odd) > td {
            background-color: var(--gray-50);
        }

        /* Badges */
        .badge {
            font-weight: 500;
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius);
            font-size: 0.75rem;
        }

        .bg-success {
            background-color: var(--success-color) !important;
        }

        .bg-warning {
            background-color: var(--warning-color) !important;
            color: var(--gray-900) !important;
        }

        /* Buttons */
        .btn {
            border-radius: var(--border-radius);
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            transition: var(--transition);
            font-size: 0.875rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }

        .btn-outline-primary {
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .btn-outline-danger {
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
            background: transparent;
        }

        .btn-outline-danger:hover {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        /* Links */
        .text-decoration-none {
            color: var(--primary-color);
            font-weight: 500;
            transition: var(--transition);
        }

        .text-decoration-none:hover {
            color: var(--primary-dark);
            text-decoration: underline !important;
        }

        /* Status indicators */
        .status-online {
            color: var(--success-color);
            font-weight: 600;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-indicator i {
            margin-right: 0.25rem;
            font-size: 0.75rem;
        }

        /* System status card special styling */
        .system-status-card h6 {
            font-size: 1.25rem;
            margin: 0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .card-stats h2 {
                font-size: 1.5rem;
            }

            .page-title {
                font-size: 1.25rem;
            }

            .camera-feed {
                height: 150px;
            }

            .navbar-nav {
                flex-direction: column;
                width: 100%;
            }

            .nav-link {
                text-align: left;
                margin: 0.125rem 0;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-desktop"></i> Face Attendance System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link active" href="/">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a class="nav-link" href="/register-user">
                        <i class="fas fa-user-plus"></i> Register User
                    </a>
                    <a class="nav-link" href="/manage-camera">
                        <i class="fas fa-video"></i> Manage Cameras
                    </a>
                    <a class="nav-link" href="/attendance">
                        <i class="fas fa-clock"></i> Attendance Logs
                    </a>
                    <a class="nav-link" href="/users">
                        <i class="fas fa-users"></i> Users
                    </a>
                    <form method="post" action="/logout" class="d-inline">
                        <button type="submit" class="nav-link btn btn-link border-0" style="background: none; color: var(--gray-700) !important; font-weight: 500; padding: 0.5rem 0.75rem; border-radius: var(--border-radius); margin: 0 0.125rem; transition: var(--transition); font-size: 0.875rem;">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title">Currently Logged In</h5>
                                <h2>{{ current_users|length }}</h2>
                            </div>
                            <div>
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title">Active Cameras</h5>
                                <h2>{{ active_cameras|length }}</h2>
                            </div>
                            <div>
                                <i class="fas fa-video fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title">Today's Attendance</h5>
                                <h2 id="today-attendance-count">{{ today_total }}</h2>
                            </div>
                            <div>
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card card-stats system-status-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title">System Status</h5>
                                <h6 class="status-online status-indicator">
                                    <i class="fas fa-circle"></i> Online
                                </h6>
                            </div>
                            <div>
                                <i class="fas fa-server fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Row -->
        <div class="row">
            <!-- Currently Logged In Users -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-check"></i> Currently Logged In</h5>
                    </div>
                    <div class="card-body" id="current-users-list">
                        {% if current_users %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Login Time</th>
                                            <th>Camera</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody id="current-users-tbody">
                                        {% for user in current_users %}
                                        <tr>
                                            <td>
                                                <a href="/user/{{ user.user_id }}" class="text-decoration-none">
                                                    {{ user.name }}
                                                </a>
                                            </td>
                                            <td>{{ user.login_time }}</td>
                                            <td>{{ user.camera_name }}</td>
                                            <td>
                                                <span class="badge bg-{% if user.camera_type == 'IN' %}success{% else %}warning{% endif %}">
                                                    {{ user.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted mb-0">No users currently logged in.</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Live Camera Feed -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-video"></i> Live Camera Feed</h5>
                        <span class="status-indicator status-online">
                            <i class="fas fa-circle"></i> Online
                        </span>
                    </div>
                    <div class="card-body">
                        {% if active_cameras %}
                            <div class="camera-feed-container">
                                <img src="/camera-stream/{{ active_cameras[0].camera_id }}"
                                     alt="{{ active_cameras[0].camera_name }}"
                                     class="camera-feed"
                                     data-camera-id="{{ active_cameras[0].camera_id }}"
                                     onload="this.style.display='block'; this.nextElementSibling.style.display='none';"
                                     onerror="handleCameraError(this);">
                                <div class="no-camera-placeholder" style="display: none;">
                                    <i class="fas fa-video-slash fa-2x mb-2"></i>
                                    <small>Camera Offline</small>
                                </div>
                                <div class="camera-label">
                                    Front Camera (IN) - Live Stream
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-stop"></i> Stop
                                    </button>
                                </div>
                            </div>
                        {% else %}
                            <div class="no-camera-placeholder">
                                <i class="fas fa-video-slash fa-2x mb-2"></i>
                                <small>No active cameras</small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Cameras -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-video"></i> Active Cameras</h5>
                    </div>
                    <div class="card-body">
                        {% if active_cameras %}
                            <div class="row">
                                {% for camera in active_cameras %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                                        <div>
                                            <span class="camera-status {% if camera.is_active %}camera-active{% else %}camera-inactive{% endif %}"></span>
                                            <strong>{{ camera.camera_name }}</strong>
                                            <br>
                                            <small class="text-muted">({{ camera.camera_type }})</small>
                                        </div>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary me-1" onclick="viewCamera({{ camera.camera_id }})">
                                                <i class="fas fa-eye"></i> View
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="stopCamera({{ camera.camera_id }})">
                                                <i class="fas fa-stop"></i> Stop
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted mb-0">No active cameras.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Attendance Logs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-history"></i> Recent Attendance Logs</h5>
                        <a href="/attendance" class="btn btn-sm btn-primary">View All</a>
                    </div>
                    <div class="card-body">
                        {% if today_last_5 %}
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Date</th>
                                            <th>Login</th>
                                            <th>Logout</th>
                                            <th>Duration</th>
                                            <th>Camera</th>
                                            <th>Type</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-attendance-tbody">
                                        {% for log in today_last_5 %}
                                        <tr>
                                            <td>
                                                <a href="/user/{{ log.user_id }}" class="text-decoration-none">
                                                    {{ log.name }}
                                                </a>
                                            </td>
                                            <td>{{ log.date }}</td>
                                            <td>{{ log.login_time or '-' }}</td>
                                            <td>{{ log.logout_time or '-' }}</td>
                                            <td>{{ log.duration or '-' }}</td>
                                            <td>{{ log.camera_name }}</td>
                                            <td>
                                                <span class="badge bg-{% if log.camera_type == 'IN' %}success{% else %}warning{% endif %}">
                                                    {{ log.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <p class="text-muted mb-0">No attendance logs for today.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCamera(cameraId) {
            window.open(`/camera-stream/${cameraId}`, '_blank', 'width=800,height=600');
        }

        function stopCamera(cameraId) {
            if (confirm('Are you sure you want to stop this camera?')) {
                fetch(`/stop-camera/${cameraId}`, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message || data.error);
                        location.reload();
                    })
                    .catch(error => {
                        alert('Error stopping camera');
                        console.error(error);
                    });
            }
        }

        // Handle camera feed errors
        function handleCameraError(img) {
            console.log('Camera feed error for camera:', img.dataset.cameraId);
            img.style.display = 'none';
            img.nextElementSibling.style.display = 'flex';

            // Try to reconnect after 3 seconds
            setTimeout(() => {
                const cameraId = img.dataset.cameraId;
                const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                img.src = newSrc;
            }, 3000);
        }

        // Initialize camera feeds with proper error handling
        function initializeCameraFeeds() {
            const cameraFeeds = document.querySelectorAll('.camera-feed');
            cameraFeeds.forEach(feed => {
                // Add timestamp to prevent caching
                const cameraId = feed.dataset.cameraId;
                feed.src = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;

                // Set up periodic refresh for each feed
                setInterval(() => {
                    if (feed.style.display !== 'none') {
                        const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                        feed.src = newSrc;
                    }
                }, 100); // Refresh every 100ms for smooth video
            });
        }

        // Initialize feeds when page loads
        document.addEventListener('DOMContentLoaded', initializeCameraFeeds);

        // Real-time dashboard updates
        function updateDashboardData() {
            // Update recent attendance logs
            fetch('/api/attendance/recent')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateRecentAttendanceLogs(data.logs, data.today_total);
                    }
                })
                .catch(error => console.error('Error fetching recent attendance:', error));

            // Update currently logged in users
            fetch('/api/attendance/current')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateCurrentlyLoggedIn(data.users);
                    }
                })
                .catch(error => console.error('Error fetching current users:', error));
        }

        function updateRecentAttendanceLogs(logs, todayTotal) {
            // Update recent attendance table
            const recentTbody = document.querySelector('#recent-attendance-tbody');
            if (recentTbody) {
                recentTbody.innerHTML = '';
                logs.forEach(log => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><a href="/user/${log.user_id}" class="text-decoration-none">${log.name}</a></td>
                        <td>${log.date}</td>
                        <td>${log.login_time || '-'}</td>
                        <td>${log.logout_time || '-'}</td>
                        <td>${log.duration || '-'}</td>
                        <td>${log.camera_name}</td>
                        <td>
                            <span class="badge ${log.camera_type === 'IN' ? 'bg-success' : 'bg-warning'}">
                                ${log.camera_type}
                            </span>
                        </td>
                    `;
                    recentTbody.appendChild(row);
                });
            }

            // Update today's attendance count in stats card using the correct selector
            const todayCountElement = document.querySelector('#today-attendance-count');
            if (todayCountElement && todayTotal !== undefined) {
                todayCountElement.textContent = todayTotal;
            }
        }

        function updateCurrentlyLoggedIn(users) {
            const currentUsersContainer = document.querySelector('#current-users-list');
            const currentUsersCount = document.querySelector('.card-stats:nth-child(1) h2');
            const currentUsersTbody = document.querySelector('#current-users-tbody');

            if (currentUsersCount) {
                currentUsersCount.textContent = users.length;
            }

            if (currentUsersContainer) {
                if (users.length === 0) {
                    currentUsersContainer.innerHTML = '<p class="text-muted">No users currently logged in.</p>';
                } else {
                    // Update the table structure
                    currentUsersContainer.innerHTML = `
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Login Time</th>
                                        <th>Camera</th>
                                        <th>Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${users.map(user => `
                                        <tr>
                                            <td>
                                                <a href="/user/${user.user_id}" class="text-decoration-none">
                                                    ${user.name}
                                                </a>
                                            </td>
                                            <td>${user.login_time}</td>
                                            <td>${user.camera_name}</td>
                                            <td>
                                                <span class="badge bg-${user.camera_type === 'IN' ? 'success' : 'warning'}">
                                                    ${user.camera_type}
                                                </span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                }
            }
        }

        // Update dashboard data every 3 seconds for real-time updates
        setInterval(updateDashboardData, 3000);

        // Initial update when page loads
        document.addEventListener('DOMContentLoaded', () => {
            updateDashboardData();
        });

        // Full page refresh every 5 minutes as backup
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
