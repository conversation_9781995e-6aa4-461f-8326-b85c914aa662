<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Attendance System - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Professional Navbar Styling */
        .navbar {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e9ecef;
            padding: 1rem 0;
        }

        .navbar-brand {
            color: #333 !important;
            font-weight: 700;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand i {
            color: #20c997;
            font-size: 1.8rem;
        }

        .navbar-nav .nav-link {
            color: #333 !important;
            font-weight: 500;
            padding: 0.8rem 1.2rem !important;
            margin: 0 0.2rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(32, 201, 151, 0.1);
            color: #20c997 !important;
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white !important;
            box-shadow: 0 3px 10px rgba(32, 201, 151, 0.3);
        }

        .navbar-nav .nav-link.active:hover {
            background: linear-gradient(135deg, #17a2b8, #20c997);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link i {
            font-size: 0.9rem;
        }

        /* Logout button styling */
        .navbar-nav button.nav-link {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            color: white !important;
            border: none;
            font-weight: 500;
        }

        .navbar-nav button.nav-link:hover {
            background: linear-gradient(135deg, #c82333, #dc3545) !important;
            transform: translateY(-1px);
        }

        /* Main Container */
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1.5rem;
            background: #f8f9fa;
        }

        /* Header Section */
        .dashboard-header {
            margin-bottom: 2rem;
            text-align: center;
        }

        .dashboard-title {
            font-size: 2.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.3rem;
            line-height: 1.1;
        }

        .dashboard-subtitle {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0;
        }

        /* Stats Cards Section */
        .stats-section {
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
            padding: 1.2rem;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            pointer-events: none;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(32, 201, 151, 0.3);
        }

        .stat-icon {
            font-size: 1.8rem;
            margin-bottom: 0.6rem;
            opacity: 0.8;
            position: relative;
            z-index: 1;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
            position: relative;
            z-index: 1;
        }

        .stat-label {
            font-size: 0.8rem;
            font-weight: 500;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* Main Content Layout */
        .main-content-section {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Currently Logged In Users - Main Focus */
        .users-main-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .users-main-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }

        .card-header-main {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e3e6f0;
        }

        .card-header-main h3 {
            color: #333;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-header-main i {
            color: #20c997;
        }

        .card-body-main {
            padding: 1.5rem;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
        }

        .users-table th {
            background: #f8f9fa;
            color: #333;
            font-weight: 600;
            padding: 1rem;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
            font-size: 0.9rem;
        }

        .users-table td {
            padding: 1rem;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
        }

        .users-table tr:hover {
            background: #f8f9fa;
        }

        .user-link {
            color: #333;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .user-link:hover {
            color: #20c997;
        }

        .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.in {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .status-badge.out {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.2);
        }

        /* Camera Sidebar */
        .camera-sidebar {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .camera-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .camera-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }

        .camera-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 0.8rem 1.2rem;
            border-bottom: 1px solid #e3e6f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .camera-header h4 {
            color: #333;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }

        .camera-header i {
            color: #20c997;
        }

        .status-online {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.7rem;
            color: #28a745;
            font-weight: 600;
        }

        .status-online i {
            font-size: 0.5rem;
        }

        .camera-body {
            padding: 1.2rem;
        }

        /* Live Camera Feed */
        .camera-feed-container {
            position: relative;
            margin-bottom: 0.8rem;
        }

        .camera-feed {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .camera-feed:hover {
            border-color: #20c997;
        }

        .camera-overlay {
            position: absolute;
            top: 8px;
            left: 8px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .camera-placeholder {
            width: 100%;
            height: 150px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #6c757d;
        }

        .camera-placeholder i {
            font-size: 1.5rem;
            margin-bottom: 0.4rem;
            opacity: 0.5;
        }

        .camera-controls {
            display: flex;
            gap: 0.4rem;
        }

        .btn-camera {
            flex: 1;
            padding: 0.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.3rem;
        }

        .btn-view {
            background: linear-gradient(135deg, #20c997, #17a2b8);
            color: white;
        }

        .btn-view:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(32, 201, 151, 0.3);
        }

        .btn-stop {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .btn-stop:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
        }

        /* Active Cameras List */
        .cameras-list {
            display: flex;
            flex-direction: column;
            gap: 0.6rem;
        }

        .camera-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem;
            background: #f8f9fa;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .camera-item:hover {
            background: #e9ecef;
            transform: translateX(2px);
        }

        .camera-info {
            display: flex;
            align-items: center;
            gap: 0.6rem;
        }

        .camera-status {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .camera-status.active {
            background: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
        }

        .camera-status.inactive {
            background: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
        }

        .camera-details h5 {
            font-size: 0.8rem;
            font-weight: 600;
            color: #333;
            margin: 0 0 0.1rem 0;
        }

        .camera-details small {
            color: #666;
            font-size: 0.7rem;
        }

        .camera-actions {
            display: flex;
            gap: 0.25rem;
        }

        .btn-icon {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.7rem;
        }

        .btn-icon.view {
            background: #e3f2fd;
            color: #1976d2;
        }

        .btn-icon.view:hover {
            background: #1976d2;
            color: white;
        }

        .btn-icon.stop {
            background: #ffebee;
            color: #d32f2f;
        }

        .btn-icon.stop:hover {
            background: #d32f2f;
            color: white;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.3;
        }

        .empty-state p {
            font-size: 1rem;
            margin: 0;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-content-section {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .camera-sidebar {
                flex-direction: row;
                overflow-x: auto;
            }

            .camera-card {
                min-width: 250px;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 1rem;
            }

            .dashboard-title {
                font-size: 1.8rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 0.8rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-icon {
                font-size: 1.5rem;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .camera-sidebar {
                flex-direction: column;
            }

            .camera-card {
                min-width: auto;
            }

            .navbar-nav .nav-link {
                padding: 0.6rem 1rem !important;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-eye"></i> VigilantEye
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link active fw-bold" href="/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="/register-user">
                    <i class="fas fa-user-plus"></i> Register User
                </a>
                <a class="nav-link" href="/manage-camera">
                    <i class="fas fa-video"></i> Manage Cameras
                </a>
                <a class="nav-link" href="/attendance">
                    <i class="fas fa-clock"></i> Attendance Logs
                </a>
                <a class="nav-link" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
                <div class="navbar-nav ms-3">
                    <form method="post" action="/logout" style="margin: 0;">
                        <button type="submit" class="nav-link btn btn-link text-light border-0" style="background: none;">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <!-- Header Section -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">
                <i class="fas fa-eye"></i> VigilantEye
            </h1>
            <p class="dashboard-subtitle">Face Attendance System Dashboard</p>
        </div>

        <!-- Stats Cards Section -->
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number">{{ current_users|length }}</div>
                    <div class="stat-label">Currently Logged In</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="stat-number">{{ active_cameras|length }}</div>
                    <div class="stat-label">Active Cameras</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number" id="today-attendance-count">{{ today_total }}</div>
                    <div class="stat-label">Today's Attendance</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stat-number">
                        <i class="fas fa-circle" style="color: #28a745; font-size: 1rem;"></i>
                    </div>
                    <div class="stat-label">System Online</div>
                </div>
            </div>
        </div>

        <!-- Main Content Layout -->
        <div class="main-content-section">
            <!-- Currently Logged In Users - Main Focus Area -->
            <div class="users-main-card">
                <div class="card-header-main">
                    <h3><i class="fas fa-user-check"></i> Currently Logged In</h3>
                </div>
                <div class="card-body-main" id="current-users-list">
                    {% if current_users %}
                        <table class="users-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Login Time</th>
                                    <th>Camera</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody id="current-users-tbody">
                                {% for user in current_users %}
                                <tr>
                                    <td>
                                        <a href="/user/{{ user.user_id }}" class="user-link">
                                            {{ user.name }}
                                        </a>
                                    </td>
                                    <td>{{ user.login_time }}</td>
                                    <td>{{ user.camera_name }}</td>
                                    <td>
                                        <span class="status-badge {% if user.camera_type == 'IN' %}in{% else %}out{% endif %}">
                                            {{ user.camera_type }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <p>No users currently logged in.</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Camera Sidebar -->
            <div class="camera-sidebar">
                <!-- Live Camera Feed -->
                <div class="camera-card">
                    <div class="camera-header">
                        <h4><i class="fas fa-video"></i> Live Camera Feed</h4>
                        <div class="status-online">
                            <i class="fas fa-circle"></i> Online
                        </div>
                    </div>
                    <div class="camera-body">
                        {% if active_cameras %}
                            <div class="camera-feed-container">
                                <img src="/camera-stream/{{ active_cameras[0].camera_id }}"
                                     alt="{{ active_cameras[0].camera_name }}"
                                     class="camera-feed"
                                     data-camera-id="{{ active_cameras[0].camera_id }}"
                                     onload="this.style.display='block'; this.nextElementSibling.style.display='none';"
                                     onerror="handleCameraError(this);">
                                <div class="camera-placeholder" style="display: none;">
                                    <i class="fas fa-video-slash"></i>
                                    <small>Camera Offline</small>
                                </div>
                                <div class="camera-overlay">
                                    {{ active_cameras[0].camera_name }} ({{ active_cameras[0].camera_type }})
                                </div>
                            </div>
                            <div class="camera-controls">
                                <button type="button" class="btn-camera btn-view" onclick="viewCamera({{ active_cameras[0].camera_id }})">
                                    <i class="fas fa-expand"></i> View
                                </button>
                                <button type="button" class="btn-camera btn-stop" onclick="stopCamera({{ active_cameras[0].camera_id }})">
                                    <i class="fas fa-stop"></i> Stop
                                </button>
                            </div>
                        {% else %}
                            <div class="camera-placeholder">
                                <i class="fas fa-video-slash"></i>
                                <small>No active cameras</small>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Active Cameras List -->
                <div class="camera-card">
                    <div class="camera-header">
                        <h4><i class="fas fa-list"></i> Active Cameras</h4>
                    </div>
                    <div class="camera-body">
                        {% if active_cameras %}
                            <div class="cameras-list">
                                {% for camera in active_cameras %}
                                <div class="camera-item">
                                    <div class="camera-info">
                                        <div class="camera-status {% if camera.is_active %}active{% else %}inactive{% endif %}"></div>
                                        <div class="camera-details">
                                            <h5>{{ camera.camera_name }}</h5>
                                            <small>({{ camera.camera_type }})</small>
                                        </div>
                                    </div>
                                    <div class="camera-actions">
                                        <button class="btn-icon view" onclick="viewCamera({{ camera.camera_id }})" title="View Camera">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon stop" onclick="stopCamera({{ camera.camera_id }})" title="Stop Camera">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="empty-state">
                                <i class="fas fa-video-slash"></i>
                                <p>No active cameras.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Attendance Logs -->
        <div class="users-main-card" style="margin-top: 2rem;">
            <div class="card-header-main">
                <h3>
                    <i class="fas fa-history"></i> Recent Attendance Logs
                    <a href="/attendance" style="margin-left: auto; background: linear-gradient(135deg, #20c997, #17a2b8); color: white; padding: 0.5rem 1rem; border-radius: 8px; text-decoration: none; font-size: 0.8rem; font-weight: 600;">
                        <i class="fas fa-external-link-alt"></i> View All
                    </a>
                </h3>
            </div>
            <div class="card-body-main">
                {% if today_last_5 %}
                    <table class="users-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Date</th>
                                <th>Login</th>
                                <th>Logout</th>
                                <th>Duration</th>
                                <th>Camera</th>
                                <th>Type</th>
                            </tr>
                        </thead>
                        <tbody id="recent-attendance-tbody">
                            {% for log in today_last_5 %}
                            <tr>
                                <td>
                                    <a href="/user/{{ log.user_id }}" class="user-link">
                                        {{ log.name }}
                                    </a>
                                </td>
                                <td>{{ log.date }}</td>
                                <td>{{ log.login_time or '-' }}</td>
                                <td>{{ log.logout_time or '-' }}</td>
                                <td>{{ log.duration or '-' }}</td>
                                <td>{{ log.camera_name }}</td>
                                <td>
                                    <span class="status-badge {% if log.camera_type == 'IN' %}in{% else %}out{% endif %}">
                                        {{ log.camera_type }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-clock"></i>
                        <p>No attendance logs for today.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCamera(cameraId) {
            window.open(`/camera-stream/${cameraId}`, '_blank', 'width=800,height=600');
        }

        function stopCamera(cameraId) {
            if (confirm('Are you sure you want to stop this camera?')) {
                fetch(`/stop-camera/${cameraId}`, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message || data.error);
                        location.reload();
                    })
                    .catch(error => {
                        alert('Error stopping camera');
                        console.error(error);
                    });
            }
        }

        // Handle camera feed errors
        function handleCameraError(img) {
            console.log('Camera feed error for camera:', img.dataset.cameraId);
            img.style.display = 'none';
            img.nextElementSibling.style.display = 'flex';

            // Try to reconnect after 3 seconds
            setTimeout(() => {
                const cameraId = img.dataset.cameraId;
                const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                img.src = newSrc;
            }, 3000);
        }

        // Initialize camera feeds with proper error handling
        function initializeCameraFeeds() {
            const cameraFeeds = document.querySelectorAll('.camera-feed');
            cameraFeeds.forEach(feed => {
                // Add timestamp to prevent caching
                const cameraId = feed.dataset.cameraId;
                feed.src = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;

                // Set up periodic refresh for each feed
                setInterval(() => {
                    if (feed.style.display !== 'none') {
                        const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                        feed.src = newSrc;
                    }
                }, 100); // Refresh every 100ms for smooth video
            });
        }

        // Initialize feeds when page loads
        document.addEventListener('DOMContentLoaded', initializeCameraFeeds);

        // Real-time dashboard updates
        function updateDashboardData() {
            // Update recent attendance logs
            fetch('/api/attendance/recent')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateRecentAttendanceLogs(data.logs, data.today_total);
                    }
                })
                .catch(error => console.error('Error fetching recent attendance:', error));

            // Update currently logged in users
            fetch('/api/attendance/current')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateCurrentlyLoggedIn(data.users);
                    }
                })
                .catch(error => console.error('Error fetching current users:', error));
        }

        function updateRecentAttendanceLogs(logs, todayTotal) {
            // Update recent attendance table
            const recentTbody = document.querySelector('#recent-attendance-tbody');
            if (recentTbody) {
                recentTbody.innerHTML = '';
                logs.forEach(log => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><a href="/user/${log.user_id}" class="user-link">${log.name}</a></td>
                        <td>${log.date}</td>
                        <td>${log.login_time || '-'}</td>
                        <td>${log.logout_time || '-'}</td>
                        <td>${log.duration || '-'}</td>
                        <td>${log.camera_name}</td>
                        <td>
                            <span class="status-badge ${log.camera_type === 'IN' ? 'in' : 'out'}">
                                ${log.camera_type}
                            </span>
                        </td>
                    `;
                    recentTbody.appendChild(row);
                });
            }

            // Update today's attendance count in stats card
            const todayCountElement = document.querySelector('#today-attendance-count');
            if (todayCountElement && todayTotal !== undefined) {
                todayCountElement.textContent = todayTotal;
            }
        }

        function updateCurrentlyLoggedIn(users) {
            const currentUsersContainer = document.querySelector('#current-users-list');
            const currentUsersCount = document.querySelector('.stat-card:first-child .stat-number');

            if (currentUsersCount) {
                currentUsersCount.textContent = users.length;
            }

            if (currentUsersContainer) {
                if (users.length === 0) {
                    currentUsersContainer.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <p>No users currently logged in.</p>
                        </div>
                    `;
                } else {
                    // Update the table structure with new design
                    currentUsersContainer.innerHTML = `
                        <table class="users-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Login Time</th>
                                    <th>Camera</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody id="current-users-tbody">
                                ${users.map(user => `
                                    <tr>
                                        <td>
                                            <a href="/user/${user.user_id}" class="user-link">
                                                ${user.name}
                                            </a>
                                        </td>
                                        <td>${user.login_time}</td>
                                        <td>${user.camera_name}</td>
                                        <td>
                                            <span class="status-badge ${user.camera_type === 'IN' ? 'in' : 'out'}">
                                                ${user.camera_type}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;
                }
            }
        }

        // Update dashboard data every 3 seconds for real-time updates
        setInterval(updateDashboardData, 3000);

        // Initial update when page loads
        document.addEventListener('DOMContentLoaded', () => {
            updateDashboardData();
        });

        // Full page refresh every 5 minutes as backup
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
