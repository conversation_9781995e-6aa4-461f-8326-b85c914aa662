from fastapi import API<PERSON>outer, Request, Form, Depends
from fastapi.responses import RedirectResponse, HTMLResponse
from fastapi.templating import Jin<PERSON>2Templates
from sqlalchemy.orm import Session
from datetime import timedelta
from app.core.database import get_database
from app.auth.auth_service import authenticate_user
from app.auth.utils import create_access_token
from app.auth.middleware import add_security_headers

templates = Jinja2Templates(directory="app/templates")
router = APIRouter()

@router.get("/login", response_class=HTMLResponse)
async def login_form(request: Request):
    """Display the login form."""
    response = templates.TemplateResponse("login.html", {"request": request})
    return add_security_headers(response)

@router.post("/login")
async def login(request: Request, username: str = Form(...), password: str = Form(...), db: Session = Depends(get_database)):
    """Handle login form submission with JWT authentication."""
    # Authenticate user
    admin = authenticate_user(db, username, password)

    if not admin:
        response = templates.TemplateResponse("login.html", {
            "request": request,
            "error": "Invalid credentials"
        })
        return add_security_headers(response)

    # Create JWT token
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": admin.username},
        expires_delta=access_token_expires
    )

    # Create redirect response
    response = RedirectResponse(url="/", status_code=302)

    # Set HTTP-only cookie with JWT token
    response.set_cookie(
        key="access_token",
        value=access_token,
        max_age=1800,  # 30 minutes
        httponly=True,
        secure=False,  # Set to True in production with HTTPS
        samesite="lax"
    )

    return add_security_headers(response)

@router.post("/logout")
async def logout_post(request: Request):
    """Handle POST logout request."""
    response = RedirectResponse(url="/login", status_code=302)

    # Clear the access token cookie with multiple methods for maximum compatibility
    response.delete_cookie(key="access_token", path="/", domain=None, httponly=True, secure=False, samesite="lax")
    response.set_cookie(key="access_token", value="", max_age=0, expires=0, path="/", domain=None, httponly=True, secure=False, samesite="lax")

    # Add comprehensive security headers including Clear-Site-Data
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, private"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    response.headers["Clear-Site-Data"] = '"cache", "cookies", "storage"'
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-Content-Type-Options"] = "nosniff"

    return response

@router.get("/logout")
async def logout_get(request: Request):
    """Handle GET logout request."""
    response = RedirectResponse(url="/login", status_code=302)

    # Clear the access token cookie
    response.delete_cookie(key="access_token", path="/", domain=None, httponly=True, secure=False, samesite="lax")
    response.set_cookie(key="access_token", value="", max_age=0, expires=0, path="/", domain=None, httponly=True, secure=False, samesite="lax")

    # Add comprehensive security headers
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate, private"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    response.headers["Clear-Site-Data"] = '"cache", "cookies", "storage"'
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-Content-Type-Options"] = "nosniff"

    return response
