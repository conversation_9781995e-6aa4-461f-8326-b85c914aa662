from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List
from datetime import datetime

# User Schemas
class UserBase(BaseModel):
    name: str
    email: EmailStr

class UserCreate(UserBase):
    pass

class UserUpdate(UserBase):
    name: Optional[str] = None
    email: Optional[EmailStr] = None

class User(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    image_path: Optional[str] = None

    class Config:
        from_attributes = True

# Camera Schemas
class CameraBase(BaseModel):
    name: str
    url: str
    type: str
    description: Optional[str] = None

    @validator('type')
    def validate_camera_type(cls, v):
        if v not in ['IN', 'OUT']:
            raise ValueError('Camera type must be either IN or OUT')
        return v

class CameraCreate(CameraBase):
    pass

class CameraUpdate(BaseModel):
    name: Optional[str] = None
    url: Optional[str] = None
    type: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

    @validator('type')
    def validate_camera_type(cls, v):
        if v is not None and v not in ['IN', 'OUT']:
            raise ValueError('Camera type must be either IN or OUT')
        return v

class Camera(CameraBase):
    id: int
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True

# Attendance Schemas
class AttendanceBase(BaseModel):
    date: str
    login_time: Optional[str] = None
    logout_time: Optional[str] = None

class AttendanceCreate(AttendanceBase):
    user_id: int
    camera_id: int

class Attendance(AttendanceBase):
    id: int
    user_id: int
    camera_id: int
    camera_name: Optional[str] = None
    camera_type: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

class AttendanceWithUser(Attendance):
    user: User

class AttendanceWithDetails(Attendance):
    user: User
    camera: Camera
    duration_formatted: Optional[str] = None

# Dashboard Schemas
class CurrentUser(BaseModel):
    user_id: int
    name: str
    email: str
    login_time: str
    camera_name: str
    camera_type: str

class DashboardStats(BaseModel):
    total_users: int
    active_cameras: int
    current_logged_in: int
    today_attendance: int

class AttendanceLog(BaseModel):
    id: int
    user_id: int
    name: str
    email: str
    date: str
    login_time: Optional[str]
    logout_time: Optional[str]
    camera_name: str
    camera_type: str
    duration: Optional[str]

# API Response Schemas
class APIResponse(BaseModel):
    status: str
    message: str
    data: Optional[dict] = None

class AttendanceResponse(APIResponse):
    user_name: Optional[str] = None
    time: Optional[str] = None
    type: Optional[str] = None  # login or logout