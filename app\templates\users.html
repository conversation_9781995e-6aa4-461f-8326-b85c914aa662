<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users - Face Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        .user-card {
            transition: transform 0.2s;
        }
        .user-card:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
        }

        /* Active navbar item styling */
        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            color: #fff !important;
        }

        .navbar-nav .nav-link.active:hover {
            background-color: rgba(255, 255, 255, 0.15);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-eye"></i> VigilantEye
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="/register-user">
                    <i class="fas fa-user-plus"></i> Register User
                </a>
                <a class="nav-link" href="/manage-camera">
                    <i class="fas fa-video"></i> Manage Cameras
                </a>
                <a class="nav-link" href="/attendance">
                    <i class="fas fa-clock"></i> Attendance Logs
                </a>
                <a class="nav-link active fw-bold" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
                <div class="navbar-nav ms-3">
                    <form method="post" action="/logout" style="margin: 0;">
                        <button type="submit" class="nav-link btn btn-link text-light border-0" style="background: none;">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="fas fa-users"></i> Users Management
                    </h1>
                    <a href="/register-user" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Add New User
                    </a>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="Search users by name or email...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="toggleView('grid')" id="gridViewBtn">
                                        <i class="fas fa-th"></i> Grid
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="toggleView('list')" id="listViewBtn">
                                        <i class="fas fa-list"></i> List
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Display -->
        <div id="usersContainer">
            {% if users %}
                <!-- Grid View (Default) -->
                <div id="gridView" class="row">
                    {% for user in users %}
                    <div class="col-md-4 col-lg-3 mb-4 user-item" 
                         data-name="{{ user.name.lower() }}" 
                         data-email="{{ user.email.lower() }}"
                         data-status="{{ 'active' if user.is_active else 'inactive' }}">
                        <div class="card user-card h-100 position-relative">
                            <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }} status-badge">
                                {{ 'Active' if user.is_active else 'Inactive' }}
                            </span>
                            
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    {% if user.image_path %}
                                        <img src="{{ user.image_path }}" alt="{{ user.name }}" class="user-avatar">
                                    {% else %}
                                        <i class="fas fa-user-circle fa-3x text-muted"></i>
                                    {% endif %}
                                </div>
                                
                                <h6 class="card-title">{{ user.name }}</h6>
                                <p class="card-text text-muted small">{{ user.email }}</p>
                                
                                <div class="btn-group w-100" role="group">
                                    <a href="/user/{{ user.id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="editUser({{ user.id }})">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- List View (Hidden by default) -->
                <div id="listView" class="d-none">
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Photo</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Status</th>
                                            <th>Registered</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for user in users %}
                                        <tr class="user-item" 
                                            data-name="{{ user.name.lower() }}" 
                                            data-email="{{ user.email.lower() }}"
                                            data-status="{{ 'active' if user.is_active else 'inactive' }}">
                                            <td>
                                                {% if user.image_path %}
                                                    <img src="{{ user.image_path }}" alt="{{ user.name }}" class="user-avatar" style="width: 40px; height: 40px;">
                                                {% else %}
                                                    <i class="fas fa-user-circle fa-2x text-muted"></i>
                                                {% endif %}
                                            </td>
                                            <td>{{ user.name }}</td>
                                            <td>{{ user.email }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }}">
                                                    {{ 'Active' if user.is_active else 'Inactive' }}
                                                </span>
                                            </td>
                                            <td>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else '-' }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="/user/{{ user.id }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-secondary" onclick="editUser({{ user.id }})">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-{{ 'danger' if user.is_active else 'success' }}" 
                                                            onclick="toggleUserStatus({{ user.id }}, {{ user.is_active|lower }})">
                                                        <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">No Users Found</h4>
                    <p class="text-muted">Get started by registering your first user.</p>
                    <a href="/register-user" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Register First User
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentView = 'grid';

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            filterUsers();
        });

        // Status filter
        document.getElementById('statusFilter').addEventListener('change', function() {
            filterUsers();
        });

        function filterUsers() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const userItems = document.querySelectorAll('.user-item');

            userItems.forEach(item => {
                const name = item.dataset.name;
                const email = item.dataset.email;
                const status = item.dataset.status;

                const matchesSearch = name.includes(searchTerm) || email.includes(searchTerm);
                const matchesStatus = !statusFilter || status === statusFilter;

                if (matchesSearch && matchesStatus) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function toggleView(view) {
            const gridView = document.getElementById('gridView');
            const listView = document.getElementById('listView');
            const gridBtn = document.getElementById('gridViewBtn');
            const listBtn = document.getElementById('listViewBtn');

            if (view === 'grid') {
                gridView.classList.remove('d-none');
                listView.classList.add('d-none');
                gridBtn.classList.remove('btn-outline-primary');
                gridBtn.classList.add('btn-primary');
                listBtn.classList.remove('btn-primary');
                listBtn.classList.add('btn-outline-primary');
            } else {
                gridView.classList.add('d-none');
                listView.classList.remove('d-none');
                listBtn.classList.remove('btn-outline-primary');
                listBtn.classList.add('btn-primary');
                gridBtn.classList.remove('btn-primary');
                gridBtn.classList.add('btn-outline-primary');
            }
            currentView = view;
        }

        function editUser(userId) {
            window.location.href = `/user/${userId}`;
        }

        function toggleUserStatus(userId, isActive) {
            const action = isActive ? 'deactivate' : 'activate';
            if (confirm(`Are you sure you want to ${action} this user?`)) {
                // TODO: Implement user status toggle
                alert(`User ${action} functionality coming soon!`);
            }
        }
    </script>
</body>
</html>
