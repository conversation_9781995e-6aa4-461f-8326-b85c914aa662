"""
Authentication service for user login and validation.
"""

from sqlalchemy.orm import Session
from app.models.models import Admin
from app.auth.utils import verify_password

def authenticate_user(db: Session, username: str, password: str) -> Admin:
    """
    Authenticate a user with username and password.
    Returns the admin user if authentication is successful, None otherwise.
    """
    # Get admin by username
    admin = db.query(Admin).filter(Admin.username == username).first()
    
    if not admin:
        return None
    
    # Verify password
    if not verify_password(password, admin.password_hash):
        return None
    
    return admin

def get_admin_by_username(db: Session, username: str) -> Admin:
    """Get admin user by username."""
    return db.query(Admin).filter(Admin.username == username).first()

def create_admin(db: Session, username: str, email: str, password: str, full_name: str, is_super_admin: bool = False) -> Admin:
    """Create a new admin user."""
    from app.auth.utils import hash_password
    
    hashed_password = hash_password(password)
    admin = Admin(
        username=username,
        email=email,
        password_hash=hashed_password,
        full_name=full_name,
        is_super_admin=is_super_admin
    )
    
    db.add(admin)
    db.commit()
    db.refresh(admin)
    return admin
